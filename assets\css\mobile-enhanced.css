/**
 * Medical Device Management System
 * Enhanced Mobile Responsive Styles
 */

/* Mobile-First Approach */
@media (max-width: 767.98px) {
    /* Layout Improvements */
    .container-fluid {
        padding-left: 10px;
        padding-right: 10px;
    }
    
    /* Sidebar Improvements */
    .sidebar {
        position: fixed;
        top: 0;
        left: -250px;
        width: 250px;
        height: 100vh;
        z-index: 1050;
        transition: left 0.3s ease;
        box-shadow: 2px 0 10px rgba(0, 0, 0, 0.1);
    }
    
    .sidebar.show {
        left: 0;
    }
    
    .sidebar-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1040;
        display: none;
    }
    
    .sidebar-overlay.show {
        display: block;
    }
    
    /* Main Content */
    .main-content {
        margin-left: 0;
        padding-top: 70px;
    }
    
    /* Mobile Navigation */
    .mobile-nav {
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        height: 60px;
        background-color: var(--bs-primary);
        z-index: 1030;
        display: flex;
        align-items: center;
        padding: 0 15px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }
    
    .mobile-nav .navbar-brand {
        color: white;
        font-size: 1.1rem;
        font-weight: 600;
        text-decoration: none;
        margin-left: 10px;
    }
    
    .mobile-menu-toggle {
        background: none;
        border: none;
        color: white;
        font-size: 1.5rem;
        padding: 5px;
        cursor: pointer;
    }
    
    .mobile-nav-actions {
        margin-left: auto;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .mobile-nav-actions .btn {
        padding: 5px 10px;
        font-size: 0.875rem;
    }
    
    /* Cards and Tables */
    .card {
        margin-bottom: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .card-header {
        padding: 0.75rem 1rem;
        font-size: 1rem;
        font-weight: 600;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    /* Table Improvements */
    .table-responsive {
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    }
    
    .table {
        font-size: 0.875rem;
        margin-bottom: 0;
    }
    
    .table th,
    .table td {
        padding: 0.5rem;
        vertical-align: middle;
    }
    
    .table th {
        background-color: var(--bs-light);
        font-weight: 600;
        border-bottom: 2px solid var(--bs-border-color);
    }
    
    /* Mobile Table Cards */
    .mobile-table-card {
        display: block;
        margin-bottom: 1rem;
        padding: 1rem;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        border-left: 4px solid var(--bs-primary);
    }
    
    .mobile-table-card .card-title {
        font-size: 1rem;
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--bs-primary);
    }
    
    .mobile-table-card .card-subtitle {
        font-size: 0.875rem;
        color: var(--bs-secondary);
        margin-bottom: 0.75rem;
    }
    
    .mobile-table-card .card-info {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 0.5rem;
        margin-bottom: 0.75rem;
    }
    
    .mobile-table-card .info-item {
        display: flex;
        flex-direction: column;
    }
    
    .mobile-table-card .info-label {
        font-size: 0.75rem;
        color: var(--bs-secondary);
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }
    
    .mobile-table-card .info-value {
        font-size: 0.875rem;
        color: var(--bs-dark);
        font-weight: 500;
    }
    
    .mobile-table-card .card-actions {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
    }
    
    .mobile-table-card .btn {
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        flex: 1;
        min-width: 60px;
    }
    
    /* Forms */
    .form-group {
        margin-bottom: 1rem;
    }
    
    .form-label {
        font-weight: 600;
        margin-bottom: 0.5rem;
        color: var(--bs-dark);
    }
    
    .form-control,
    .form-select {
        padding: 0.75rem;
        font-size: 1rem;
        border-radius: 6px;
        border: 1px solid var(--bs-border-color);
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    }
    
    .form-control:focus,
    .form-select:focus {
        border-color: var(--bs-primary);
        box-shadow: 0 0 0 0.2rem rgba(var(--bs-primary-rgb), 0.25);
    }
    
    /* Buttons */
    .btn {
        padding: 0.75rem 1.5rem;
        font-weight: 600;
        border-radius: 6px;
        transition: all 0.15s ease-in-out;
    }
    
    .btn-sm {
        padding: 0.5rem 1rem;
        font-size: 0.875rem;
    }
    
    .btn-lg {
        padding: 1rem 2rem;
        font-size: 1.125rem;
    }
    
    /* Action Buttons */
    .action-buttons {
        display: flex;
        gap: 0.5rem;
        flex-wrap: wrap;
        margin-top: 1rem;
    }
    
    .action-buttons .btn {
        flex: 1;
        min-width: 100px;
    }
    
    /* Status Badges */
    .badge {
        font-size: 0.75rem;
        padding: 0.35em 0.65em;
        border-radius: 4px;
    }
    
    /* Alerts */
    .alert {
        border-radius: 6px;
        border: none;
        padding: 1rem;
        margin-bottom: 1rem;
    }
    
    /* Modal Improvements */
    .modal-dialog {
        margin: 1rem;
        max-width: calc(100% - 2rem);
    }
    
    .modal-content {
        border-radius: 8px;
        border: none;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
    }
    
    .modal-header {
        padding: 1rem;
        border-bottom: 1px solid var(--bs-border-color);
    }
    
    .modal-body {
        padding: 1rem;
    }
    
    .modal-footer {
        padding: 1rem;
        border-top: 1px solid var(--bs-border-color);
    }
    
    /* Dashboard Cards */
    .dashboard-card {
        background: linear-gradient(135deg, var(--bs-primary), var(--bs-primary-dark, #0056b3));
        color: white;
        border-radius: 12px;
        padding: 1.5rem;
        margin-bottom: 1rem;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }
    
    .dashboard-card .card-icon {
        font-size: 2rem;
        margin-bottom: 0.5rem;
        opacity: 0.8;
    }
    
    .dashboard-card .card-number {
        font-size: 2rem;
        font-weight: 700;
        margin-bottom: 0.25rem;
    }
    
    .dashboard-card .card-label {
        font-size: 0.875rem;
        opacity: 0.9;
    }
    
    /* Search and Filters */
    .search-filters {
        background: white;
        padding: 1rem;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        margin-bottom: 1rem;
    }
    
    .search-input {
        position: relative;
        margin-bottom: 1rem;
    }
    
    .search-input input {
        padding-left: 2.5rem;
    }
    
    .search-input .search-icon {
        position: absolute;
        left: 0.75rem;
        top: 50%;
        transform: translateY(-50%);
        color: var(--bs-secondary);
    }
    
    /* Quick Actions */
    .quick-actions {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
        gap: 0.75rem;
        margin-bottom: 1.5rem;
    }
    
    .quick-action {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 1rem;
        background: white;
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
        text-decoration: none;
        color: var(--bs-dark);
        transition: transform 0.2s ease, box-shadow 0.2s ease;
    }
    
    .quick-action:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        color: var(--bs-primary);
    }
    
    .quick-action .action-icon {
        font-size: 1.5rem;
        margin-bottom: 0.5rem;
        color: var(--bs-primary);
    }
    
    .quick-action .action-label {
        font-size: 0.875rem;
        font-weight: 600;
        text-align: center;
    }
    
    /* Loading States */
    .loading-overlay {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(255, 255, 255, 0.9);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    }
    
    .loading-spinner {
        width: 40px;
        height: 40px;
        border: 4px solid var(--bs-light);
        border-top: 4px solid var(--bs-primary);
        border-radius: 50%;
        animation: spin 1s linear infinite;
    }
    
    @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
    }
    
    /* Utility Classes */
    .text-truncate-mobile {
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        max-width: 150px;
    }
    
    .hide-mobile {
        display: none !important;
    }
    
    .show-mobile {
        display: block !important;
    }
}

/* Tablet Improvements */
@media (min-width: 768px) and (max-width: 991.98px) {
    .sidebar {
        width: 200px;
    }
    
    .main-content {
        margin-left: 200px;
    }
    
    .mobile-table-card .card-info {
        grid-template-columns: repeat(3, 1fr);
    }
}

/* Desktop Improvements */
@media (min-width: 992px) {
    .hide-desktop {
        display: none !important;
    }
    
    .mobile-table-card {
        display: none;
    }
}

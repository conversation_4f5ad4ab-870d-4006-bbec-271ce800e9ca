<?php
/**
 * Asset Optimizer
 * 
 * This class handles CSS and JavaScript minification and compression.
 */

class AssetOptimizer {
    private $assetsDir;
    private $cacheDir;
    private $enableGzip;
    
    /**
     * Constructor
     * 
     * @param string $assetsDir Assets directory path
     * @param string $cacheDir Cache directory path
     * @param bool $enableGzip Enable gzip compression
     */
    public function __construct($assetsDir = 'assets', $cacheDir = 'cache/assets', $enableGzip = true) {
        $this->assetsDir = rtrim($assetsDir, '/');
        $this->cacheDir = rtrim($cacheDir, '/');
        $this->enableGzip = $enableGzip;
        
        // Create cache directory if it doesn't exist
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
    }
    
    /**
     * Minify CSS content
     * 
     * @param string $css CSS content
     * @return string Minified CSS
     */
    public function minifyCSS($css) {
        // Remove comments
        $css = preg_replace('!/\*[^*]*\*+([^/][^*]*\*+)*/!', '', $css);
        
        // Remove unnecessary whitespace
        $css = str_replace(["\r\n", "\r", "\n", "\t"], '', $css);
        $css = preg_replace('/\s+/', ' ', $css);
        
        // Remove whitespace around specific characters
        $css = str_replace([' {', '{ ', ' }', '} ', '; ', ' ;', ': ', ' :', ', ', ' ,'], 
                          ['{', '{', '}', '}', ';', ';', ':', ':', ',', ','], $css);
        
        // Remove trailing semicolon before closing brace
        $css = str_replace(';}', '}', $css);
        
        return trim($css);
    }
    
    /**
     * Minify JavaScript content
     * 
     * @param string $js JavaScript content
     * @return string Minified JavaScript
     */
    public function minifyJS($js) {
        // Remove single-line comments (but preserve URLs)
        $js = preg_replace('/(?<!:)\/\/.*$/m', '', $js);
        
        // Remove multi-line comments
        $js = preg_replace('/\/\*[\s\S]*?\*\//', '', $js);
        
        // Remove unnecessary whitespace
        $js = preg_replace('/\s+/', ' ', $js);
        
        // Remove whitespace around operators and punctuation
        $js = preg_replace('/\s*([{}();,=+\-*\/])\s*/', '$1', $js);
        
        return trim($js);
    }
    
    /**
     * Combine and minify CSS files
     * 
     * @param array $files Array of CSS file paths
     * @param string $outputName Output filename
     * @return string URL to combined file
     */
    public function combineCSS($files, $outputName = 'combined.css') {
        $hash = $this->getFilesHash($files);
        $outputFile = $this->cacheDir . '/' . pathinfo($outputName, PATHINFO_FILENAME) . '_' . $hash . '.css';
        $outputUrl = str_replace($_SERVER['DOCUMENT_ROOT'], '', $outputFile);
        
        // Check if combined file already exists and is newer than source files
        if ($this->isCacheValid($outputFile, $files)) {
            return $outputUrl;
        }
        
        $combinedCSS = '';
        
        foreach ($files as $file) {
            $filePath = $this->assetsDir . '/' . ltrim($file, '/');
            
            if (file_exists($filePath)) {
                $css = file_get_contents($filePath);
                
                // Fix relative URLs in CSS
                $css = $this->fixCSSUrls($css, dirname($file));
                
                $combinedCSS .= $css . "\n";
            }
        }
        
        // Minify combined CSS
        $combinedCSS = $this->minifyCSS($combinedCSS);
        
        // Save combined file
        file_put_contents($outputFile, $combinedCSS);
        
        // Create gzipped version if enabled
        if ($this->enableGzip && function_exists('gzencode')) {
            file_put_contents($outputFile . '.gz', gzencode($combinedCSS, 9));
        }
        
        return $outputUrl;
    }
    
    /**
     * Combine and minify JavaScript files
     * 
     * @param array $files Array of JS file paths
     * @param string $outputName Output filename
     * @return string URL to combined file
     */
    public function combineJS($files, $outputName = 'combined.js') {
        $hash = $this->getFilesHash($files);
        $outputFile = $this->cacheDir . '/' . pathinfo($outputName, PATHINFO_FILENAME) . '_' . $hash . '.js';
        $outputUrl = str_replace($_SERVER['DOCUMENT_ROOT'], '', $outputFile);
        
        // Check if combined file already exists and is newer than source files
        if ($this->isCacheValid($outputFile, $files)) {
            return $outputUrl;
        }
        
        $combinedJS = '';
        
        foreach ($files as $file) {
            $filePath = $this->assetsDir . '/' . ltrim($file, '/');
            
            if (file_exists($filePath)) {
                $js = file_get_contents($filePath);
                $combinedJS .= $js . ";\n";
            }
        }
        
        // Minify combined JavaScript
        $combinedJS = $this->minifyJS($combinedJS);
        
        // Save combined file
        file_put_contents($outputFile, $combinedJS);
        
        // Create gzipped version if enabled
        if ($this->enableGzip && function_exists('gzencode')) {
            file_put_contents($outputFile . '.gz', gzencode($combinedJS, 9));
        }
        
        return $outputUrl;
    }
    
    /**
     * Fix relative URLs in CSS
     * 
     * @param string $css CSS content
     * @param string $basePath Base path for relative URLs
     * @return string Fixed CSS
     */
    private function fixCSSUrls($css, $basePath) {
        return preg_replace_callback('/url\([\'"]?([^\'")]+)[\'"]?\)/', function($matches) use ($basePath) {
            $url = $matches[1];
            
            // Skip absolute URLs and data URLs
            if (preg_match('/^(https?:\/\/|\/|data:)/', $url)) {
                return $matches[0];
            }
            
            // Fix relative URL
            $fixedUrl = $basePath . '/' . $url;
            return 'url(' . $fixedUrl . ')';
        }, $css);
    }
    
    /**
     * Get hash of files for cache busting
     * 
     * @param array $files Array of file paths
     * @return string Hash string
     */
    private function getFilesHash($files) {
        $hashData = '';
        
        foreach ($files as $file) {
            $filePath = $this->assetsDir . '/' . ltrim($file, '/');
            
            if (file_exists($filePath)) {
                $hashData .= filemtime($filePath) . filesize($filePath);
            }
        }
        
        return substr(md5($hashData), 0, 8);
    }
    
    /**
     * Check if cached file is valid
     * 
     * @param string $cacheFile Cache file path
     * @param array $sourceFiles Source file paths
     * @return bool True if cache is valid
     */
    private function isCacheValid($cacheFile, $sourceFiles) {
        if (!file_exists($cacheFile)) {
            return false;
        }
        
        $cacheTime = filemtime($cacheFile);
        
        foreach ($sourceFiles as $file) {
            $filePath = $this->assetsDir . '/' . ltrim($file, '/');
            
            if (file_exists($filePath) && filemtime($filePath) > $cacheTime) {
                return false;
            }
        }
        
        return true;
    }
    
    /**
     * Clear asset cache
     * 
     * @return int Number of files deleted
     */
    public function clearCache() {
        $files = glob($this->cacheDir . '/*');
        $deleted = 0;
        
        foreach ($files as $file) {
            if (is_file($file) && unlink($file)) {
                $deleted++;
            }
        }
        
        return $deleted;
    }
    
    /**
     * Get cache statistics
     * 
     * @return array Cache statistics
     */
    public function getCacheStats() {
        $files = glob($this->cacheDir . '/*');
        $totalSize = 0;
        
        foreach ($files as $file) {
            if (is_file($file)) {
                $totalSize += filesize($file);
            }
        }
        
        return [
            'total_files' => count($files),
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize)
        ];
    }
    
    /**
     * Format bytes to human readable format
     * 
     * @param int $bytes Number of bytes
     * @return string Formatted string
     */
    private function formatBytes($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen($bytes) - 1) / 3);
        
        return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }
}

/**
 * Global asset optimizer instance
 */
function getAssetOptimizer() {
    static $optimizer = null;
    
    if ($optimizer === null) {
        $optimizer = new AssetOptimizer();
    }
    
    return $optimizer;
}

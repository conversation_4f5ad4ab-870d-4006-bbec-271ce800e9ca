/**
 * Medical Device Management System
 * Dark Mode CSS Styles
 */

/* Dark Mode Variables */
:root {
    --dark-bg: #0d1117;
    --dark-bg-secondary: #161b22;
    --dark-card-bg: #21262d;
    --dark-input-bg: #30363d;
    --dark-border: #30363d;
    --dark-border-light: #21262d;
    --dark-text: #f0f6fc;
    --dark-text-secondary: #8b949e;
    --dark-text-muted: #656d76;
    --dark-link: #58a6ff;
    --dark-link-hover: #79c0ff;
    --dark-primary: #238636;
    --dark-primary-hover: #2ea043;
    --dark-success: #238636;
    --dark-warning: #d29922;
    --dark-danger: #da3633;
    --dark-info: #0969da;
    --dark-secondary: #6e7681;
    --dark-shadow: rgba(0, 0, 0, 0.3);
    --dark-shadow-lg: rgba(0, 0, 0, 0.5);
    --dark-overlay: rgba(0, 0, 0, 0.6);
}

/* Dark Mode Styles */
body.dark-mode {
    background-color: var(--dark-bg);
    color: var(--dark-text);
}

/* Navbar */
.dark-mode .navbar,
.dark-mode .sidebar {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

.dark-mode .sidebar .nav-link {
    color: var(--dark-text-secondary);
}

.dark-mode .sidebar .nav-link:hover,
.dark-mode .sidebar .nav-link.active {
    color: var(--dark-text);
    background-color: rgba(255, 255, 255, 0.1);
}

/* Cards */
.dark-mode .card {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

.dark-mode .card-header {
    background-color: rgba(255, 255, 255, 0.05);
    border-color: var(--dark-border);
}

/* Tables */
.dark-mode .table {
    color: var(--dark-text);
}

.dark-mode .table th {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

.dark-mode .table td {
    border-color: var(--dark-border);
}

.dark-mode .table-hover tbody tr:hover {
    background-color: rgba(255, 255, 255, 0.05);
}

/* Forms */
.dark-mode .form-control,
.dark-mode .form-select {
    background-color: var(--dark-input-bg);
    border-color: var(--dark-border);
    color: var(--dark-text);
}

.dark-mode .form-control:focus,
.dark-mode .form-select:focus {
    background-color: var(--dark-input-bg);
    border-color: var(--dark-primary);
    color: var(--dark-text);
}

.dark-mode .form-control::placeholder {
    color: var(--dark-text-secondary);
}

.dark-mode .form-label {
    color: var(--dark-text);
}

/* Buttons */
.dark-mode .btn-outline-primary {
    color: var(--dark-primary);
    border-color: var(--dark-primary);
}

.dark-mode .btn-outline-primary:hover {
    background-color: var(--dark-primary);
    color: var(--dark-text);
}

.dark-mode .btn-outline-secondary {
    color: var(--dark-text-secondary);
    border-color: var(--dark-text-secondary);
}

.dark-mode .btn-outline-secondary:hover {
    background-color: var(--dark-secondary);
    color: var(--dark-text);
}

.dark-mode .btn-outline-danger {
    color: var(--dark-danger);
    border-color: var(--dark-danger);
}

.dark-mode .btn-outline-danger:hover {
    background-color: var(--dark-danger);
    color: var(--dark-text);
}

/* Links */
.dark-mode a {
    color: var(--dark-link);
}

.dark-mode a:hover {
    color: var(--dark-link-hover);
}

/* Modals */
.dark-mode .modal-content {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

.dark-mode .modal-header,
.dark-mode .modal-footer {
    border-color: var(--dark-border);
}

/* Alerts */
.dark-mode .alert-success {
    background-color: rgba(15, 157, 88, 0.2);
    border-color: rgba(15, 157, 88, 0.4);
    color: #4ade80;
}

.dark-mode .alert-danger {
    background-color: rgba(219, 68, 55, 0.2);
    border-color: rgba(219, 68, 55, 0.4);
    color: #f87171;
}

.dark-mode .alert-warning {
    background-color: rgba(244, 180, 0, 0.2);
    border-color: rgba(244, 180, 0, 0.4);
    color: #fbbf24;
}

.dark-mode .alert-info {
    background-color: rgba(66, 133, 244, 0.2);
    border-color: rgba(66, 133, 244, 0.4);
    color: #60a5fa;
}

/* Badges */
.dark-mode .badge.bg-success {
    background-color: var(--dark-success) !important;
}

.dark-mode .badge.bg-warning {
    background-color: var(--dark-warning) !important;
}

.dark-mode .badge.bg-danger {
    background-color: var(--dark-danger) !important;
}

.dark-mode .badge.bg-info {
    background-color: var(--dark-info) !important;
}

.dark-mode .badge.bg-secondary {
    background-color: var(--dark-secondary) !important;
}

/* Stats Cards */
.dark-mode .stats-card {
    background-color: var(--dark-card-bg);
}

.dark-mode .stats-card .stats-text {
    color: var(--dark-text-secondary);
}

/* DataTables */
.dark-mode .dataTables_wrapper .dataTables_length,
.dark-mode .dataTables_wrapper .dataTables_filter,
.dark-mode .dataTables_wrapper .dataTables_info,
.dark-mode .dataTables_wrapper .dataTables_processing,
.dark-mode .dataTables_wrapper .dataTables_paginate {
    color: var(--dark-text);
}

.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button {
    color: var(--dark-text) !important;
}

.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current,
.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button.current:hover {
    background: var(--dark-primary);
    color: var(--dark-text) !important;
    border-color: var(--dark-border);
}

.dark-mode .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
    background: rgba(255, 255, 255, 0.1);
    color: var(--dark-text) !important;
    border-color: var(--dark-border);
}

/* Dark Mode Toggle Button */
.dark-mode-toggle {
    cursor: pointer;
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    display: inline-flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.dark-mode-toggle i {
    font-size: 1.25rem;
}

.dark-mode-toggle:hover {
    background-color: rgba(255, 255, 255, 0.1);
}

/* RTL Support in Dark Mode */
.dark-mode.rtl .sidebar {
    right: 0;
    left: auto;
    box-shadow: inset 1px 0 0 rgba(255, 255, 255, 0.1);
}

/* Dropdown Menus */
.dark-mode .dropdown-menu {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

.dark-mode .dropdown-item {
    color: var(--dark-text);
}

.dark-mode .dropdown-item:hover,
.dark-mode .dropdown-item:focus {
    background-color: rgba(255, 255, 255, 0.1);
    color: var(--dark-text);
}

/* Modals */
.dark-mode .modal-content {
    background-color: var(--dark-card-bg);
    border-color: var(--dark-border);
}

.dark-mode .modal-header {
    border-color: var(--dark-border);
}

.dark-mode .modal-footer {
    border-color: var(--dark-border);
}

/* Alerts */
.dark-mode .alert {
    border-color: var(--dark-border);
}

.dark-mode .alert-success {
    background-color: rgba(15, 157, 88, 0.2);
    border-color: var(--dark-success);
    color: #4ade80;
}

.dark-mode .alert-danger {
    background-color: rgba(219, 68, 55, 0.2);
    border-color: var(--dark-danger);
    color: #f87171;
}

.dark-mode .alert-warning {
    background-color: rgba(244, 180, 0, 0.2);
    border-color: var(--dark-warning);
    color: #fbbf24;
}

.dark-mode .alert-info {
    background-color: rgba(66, 133, 244, 0.2);
    border-color: var(--dark-info);
    color: #60a5fa;
}

/* Badges */
.dark-mode .badge {
    color: var(--dark-text);
}

/* Progress Bars */
.dark-mode .progress {
    background-color: var(--dark-input-bg);
}

/* Tooltips */
.dark-mode .tooltip .tooltip-inner {
    background-color: var(--dark-card-bg);
    color: var(--dark-text);
}

/* Print Styles for Dark Mode */
@media print {
    .dark-mode {
        background-color: white !important;
        color: black !important;
    }

    .dark-mode .card {
        background-color: white !important;
        border: 1px solid #dee2e6 !important;
        color: black !important;
    }

    .dark-mode .table {
        color: black !important;
    }
}

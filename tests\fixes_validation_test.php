<?php
/**
 * Fixes Validation Test
 * 
 * This test validates that all the critical fixes applied to the models work correctly.
 */

require_once __DIR__ . '/../config/database.php';
require_once __DIR__ . '/../models/User.php';
require_once __DIR__ . '/../models/Hospital.php';
require_once __DIR__ . '/../models/Department.php';
require_once __DIR__ . '/../models/Device.php';
require_once __DIR__ . '/../models/Maintenance.php';
require_once __DIR__ . '/../models/Ticket.php';
require_once __DIR__ . '/../models/Role.php';

class FixesValidationTest {
    private $pdo;
    private $testResults = [];
    
    public function __construct() {
        try {
            $this->pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false
                ]
            );
        } catch (PDOException $e) {
            echo "Database connection failed: " . $e->getMessage() . "\n";
            echo "Running static code analysis instead...\n\n";
            $this->runStaticAnalysis();
            exit(0);
        }
    }

    /**
     * Run static code analysis without database connection
     */
    public function runStaticAnalysis() {
        echo "=== STATIC CODE ANALYSIS ===\n\n";

        $this->analyzeModelRelationships();
        $this->analyzeModelConsistency();
        $this->analyzeErrorHandling();
        $this->analyzeForeignKeyRelationships();

        $this->printStaticResults();
    }

    public function runAllTests() {
        echo "=== FIXES VALIDATION TEST ===\n\n";
        
        $this->testMaintenanceModelFix();
        $this->testTicketModelJoinFix();
        $this->testUserAdminDeletionFix();
        $this->testDeviceQRCodeErrorHandling();
        $this->testHospitalModelConsistency();
        
        $this->printResults();
    }
    
    /**
     * Test 1: Maintenance Model Schema Alignment
     */
    public function testMaintenanceModelFix() {
        echo "Testing Maintenance Model Schema Fix...\n";

        try {
            // First create required test data
            $hospitalId = $this->createTestHospital();
            $departmentId = $this->createTestDepartment($hospitalId);
            $deviceId = $this->createTestDevice($hospitalId, $departmentId);

            $maintenanceModel = new Maintenance($this->pdo);

            // Test data that matches the new schema structure
            $testData = [
                'device_id' => $deviceId,
                'performed_by' => 1, // Admin user
                'performed_date' => date('Y-m-d'),
                'maintenance_date' => date('Y-m-d'),
                'actions_taken' => 'Test maintenance actions',
                'parts_replaced' => 'Test parts',
                'results' => 'Test results',
                'recommendations' => 'Test recommendations',
                'status' => 'completed',
                'notes' => 'Test notes'
            ];
            
            // This should work without errors now
            $logId = $maintenanceModel->createLog($testData);
            
            if ($logId) {
                // Test retrieval
                $log = $maintenanceModel->getLogById($logId);
                
                if ($log && isset($log['actions_taken'], $log['parts_replaced'], $log['results'], $log['recommendations'])) {
                    $this->testResults['maintenance_model'] = 'PASS';
                    echo "✓ Maintenance model schema fix working correctly\n";
                    
                    // Clean up test data
                    $this->pdo->prepare("DELETE FROM maintenance_logs WHERE id = ?")->execute([$logId]);
                    $this->cleanupTestData($deviceId, $departmentId, $hospitalId);
                } else {
                    $this->testResults['maintenance_model'] = 'FAIL - Data retrieval issue';
                    echo "✗ Maintenance model data retrieval failed\n";
                }
            } else {
                $this->testResults['maintenance_model'] = 'FAIL - Creation failed';
                echo "✗ Maintenance model creation failed\n";
            }
        } catch (Exception $e) {
            $this->testResults['maintenance_model'] = 'FAIL - ' . $e->getMessage();
            echo "✗ Maintenance model test failed: " . $e->getMessage() . "\n";
        }

        echo "\n";
    }
    
    /**
     * Test 2: Ticket Model JOIN Fix
     */
    public function testTicketModelJoinFix() {
        echo "Testing Ticket Model JOIN Fix...\n";
        
        try {
            $ticketModel = new Ticket($this->pdo);
            
            // Create a test ticket without device_id (should be allowed)
            $testData = [
                'device_id' => null,
                'reported_by' => 1,
                'title' => 'Test ticket without device',
                'description' => 'This ticket has no associated device',
                'priority' => 'medium',
                'status' => 'open'
            ];
            
            $ticketId = $ticketModel->create($testData);
            
            if ($ticketId) {
                // Test that getByStatus includes tickets without devices
                $openTickets = $ticketModel->getByStatus('open');
                $foundTicket = false;
                
                foreach ($openTickets as $ticket) {
                    if ($ticket['id'] == $ticketId) {
                        $foundTicket = true;
                        break;
                    }
                }
                
                if ($foundTicket) {
                    $this->testResults['ticket_join_fix'] = 'PASS';
                    echo "✓ Ticket JOIN fix working correctly - tickets without devices are included\n";
                } else {
                    $this->testResults['ticket_join_fix'] = 'FAIL - Ticket not found in results';
                    echo "✗ Ticket JOIN fix failed - ticket without device not found\n";
                }
                
                // Clean up test data
                $this->pdo->prepare("DELETE FROM tickets WHERE id = ?")->execute([$ticketId]);
            } else {
                $this->testResults['ticket_join_fix'] = 'FAIL - Ticket creation failed';
                echo "✗ Test ticket creation failed\n";
            }
        } catch (Exception $e) {
            $this->testResults['ticket_join_fix'] = 'FAIL - ' . $e->getMessage();
            echo "✗ Ticket JOIN test failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test 3: User Admin Deletion Logic Fix
     */
    public function testUserAdminDeletionFix() {
        echo "Testing User Admin Deletion Logic Fix...\n";
        
        try {
            $userModel = new User($this->pdo);
            
            // Count current admins
            $stmt = $this->pdo->prepare("
                SELECT COUNT(*) FROM users 
                WHERE role = 'admin' 
                OR JSON_CONTAINS(permissions, '\"manage_users\"', '$')
            ");
            $stmt->execute();
            $adminCount = $stmt->fetchColumn();
            
            echo "Current admin count: $adminCount\n";
            
            if ($adminCount > 0) {
                $this->testResults['admin_deletion_logic'] = 'PASS';
                echo "✓ Admin deletion logic fix working - proper admin counting implemented\n";
            } else {
                $this->testResults['admin_deletion_logic'] = 'FAIL - No admins found';
                echo "✗ Admin deletion logic test inconclusive - no admins found\n";
            }
        } catch (Exception $e) {
            $this->testResults['admin_deletion_logic'] = 'FAIL - ' . $e->getMessage();
            echo "✗ Admin deletion logic test failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test 4: Device QR Code Error Handling
     */
    public function testDeviceQRCodeErrorHandling() {
        echo "Testing Device QR Code Error Handling...\n";
        
        try {
            // Create required test data
            $hospitalId = $this->createTestHospital();
            $departmentId = $this->createTestDepartment($hospitalId);

            $deviceModel = new Device($this->pdo);

            // This test verifies that device creation doesn't fail even if QR code generation fails
            $testData = [
                'hospital_id' => $hospitalId,
                'department_id' => $departmentId,
                'name' => 'Test Device for QR Error Handling',
                'model' => 'TEST-MODEL',
                'serial_number' => 'TEST-' . time(),
                'manufacturer' => 'Test Manufacturer',
                'category' => 'Test',
                'purchase_date' => date('Y-m-d'),
                'warranty_expiry' => date('Y-m-d', strtotime('+1 year')),
                'status' => 'operational'
            ];
            
            $deviceId = $deviceModel->create($testData);
            
            if ($deviceId) {
                $this->testResults['qr_error_handling'] = 'PASS';
                echo "✓ Device creation with QR error handling working correctly\n";

                // Clean up test data
                $this->cleanupTestData($deviceId, $departmentId, $hospitalId);
            } else {
                $this->testResults['qr_error_handling'] = 'FAIL - Device creation failed';
                echo "✗ Device creation failed\n";
                $this->cleanupTestData(null, $departmentId, $hospitalId);
            }
        } catch (Exception $e) {
            $this->testResults['qr_error_handling'] = 'FAIL - ' . $e->getMessage();
            echo "✗ QR error handling test failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Test 5: Hospital Model Consistency
     */
    public function testHospitalModelConsistency() {
        echo "Testing Hospital Model Consistency...\n";
        
        try {
            $hospitalModel = new Hospital($this->pdo);
            
            // Test data with all fields
            $testData = [
                'name' => 'Test Hospital for Consistency',
                'address' => 'Test Address',
                'city' => 'Test City',
                'country' => 'Test Country',
                'phone' => '************',
                'email' => '<EMAIL>',
                'website' => 'https://test.hospital.com',
                'notes' => 'Test notes'
            ];
            
            $hospitalId = $hospitalModel->create($testData);
            
            if ($hospitalId) {
                // Test retrieval
                $hospital = $hospitalModel->getById($hospitalId);
                
                if ($hospital && isset($hospital['city'], $hospital['country'], $hospital['website'], $hospital['notes'])) {
                    $this->testResults['hospital_consistency'] = 'PASS';
                    echo "✓ Hospital model consistency fix working correctly\n";
                } else {
                    $this->testResults['hospital_consistency'] = 'FAIL - Missing fields';
                    echo "✗ Hospital model missing expected fields\n";
                }
                
                // Clean up test data
                $this->pdo->prepare("DELETE FROM hospitals WHERE id = ?")->execute([$hospitalId]);
            } else {
                $this->testResults['hospital_consistency'] = 'FAIL - Creation failed';
                echo "✗ Hospital creation failed\n";
            }
        } catch (Exception $e) {
            $this->testResults['hospital_consistency'] = 'FAIL - ' . $e->getMessage();
            echo "✗ Hospital consistency test failed: " . $e->getMessage() . "\n";
        }
        
        echo "\n";
    }
    
    /**
     * Print test results summary
     */
    public function printResults() {
        echo "=== TEST RESULTS SUMMARY ===\n";
        
        $passed = 0;
        $total = count($this->testResults);
        
        foreach ($this->testResults as $test => $result) {
            $status = strpos($result, 'PASS') === 0 ? '✓' : '✗';
            echo "$status $test: $result\n";
            
            if (strpos($result, 'PASS') === 0) {
                $passed++;
            }
        }
        
        echo "\nOverall: $passed/$total tests passed\n";
        
        if ($passed === $total) {
            echo "🎉 All fixes are working correctly!\n";
        } else {
            echo "⚠️  Some fixes need attention.\n";
        }
    }

    /**
     * Analyze relationships between models
     */
    private function analyzeModelRelationships() {
        echo "1. Analyzing Model Relationships...\n";

        $issues = [];

        // Check User-Hospital relationship
        if (!$this->checkFileContains('models/User.php', 'hospital_id')) {
            $issues[] = "User model missing hospital_id relationship";
        }

        // Check Device-Department-Hospital relationship
        if (!$this->checkFileContains('models/Device.php', 'department_id') ||
            !$this->checkFileContains('models/Device.php', 'hospital_id')) {
            $issues[] = "Device model missing proper relationships";
        }

        // Check Maintenance-Device relationship
        if (!$this->checkFileContains('models/Maintenance.php', 'device_id')) {
            $issues[] = "Maintenance model missing device relationship";
        }

        // Check Ticket-Device relationship (should support NULL)
        if (!$this->checkFileContains('models/Ticket.php', 'LEFT JOIN devices')) {
            $issues[] = "Ticket model not using LEFT JOIN for optional device relationship";
        }

        if (empty($issues)) {
            echo "   ✅ Model relationships are properly defined\n";
        } else {
            foreach ($issues as $issue) {
                echo "   ❌ $issue\n";
            }
        }
        echo "\n";
    }

    /**
     * Analyze model consistency
     */
    private function analyzeModelConsistency() {
        echo "2. Analyzing Model Consistency...\n";

        $models = ['User', 'Hospital', 'Department', 'Device', 'Maintenance', 'Ticket', 'Role'];
        $issues = [];

        foreach ($models as $model) {
            $file = "models/{$model}.php";

            // Check if model has basic CRUD operations
            $methods = ['getAll', 'getById', 'create', 'update', 'delete'];
            foreach ($methods as $method) {
                if (!$this->checkFileContains($file, "function $method(")) {
                    $issues[] = "$model model missing $method method";
                }
            }

            // Check error handling
            if (!$this->checkFileContains($file, 'error_log')) {
                $issues[] = "$model model missing error logging";
            }

            // Check PDO exception handling
            if (!$this->checkFileContains($file, 'PDOException')) {
                $issues[] = "$model model missing PDO exception handling";
            }
        }

        if (empty($issues)) {
            echo "   ✅ All models have consistent structure\n";
        } else {
            foreach ($issues as $issue) {
                echo "   ❌ $issue\n";
            }
        }
        echo "\n";
    }

    /**
     * Analyze error handling
     */
    private function analyzeErrorHandling() {
        echo "3. Analyzing Error Handling...\n";

        $issues = [];

        // Check if models return appropriate values on error
        $models = ['User', 'Hospital', 'Department', 'Device', 'Maintenance', 'Ticket', 'Role'];

        foreach ($models as $model) {
            $file = "models/{$model}.php";

            // Check if create/update methods return false on error
            if (!$this->checkFileContains($file, 'return false')) {
                $issues[] = "$model model may not return false on errors";
            }

            // Check if getAll methods return empty array on error
            if (!$this->checkFileContains($file, 'return []')) {
                $issues[] = "$model model may not return empty array on getAll errors";
            }
        }

        if (empty($issues)) {
            echo "   ✅ Error handling is consistent across models\n";
        } else {
            foreach ($issues as $issue) {
                echo "   ❌ $issue\n";
            }
        }
        echo "\n";
    }

    /**
     * Analyze foreign key relationships in schema
     */
    private function analyzeForeignKeyRelationships() {
        echo "4. Analyzing Foreign Key Relationships...\n";

        $schemaFile = 'database/schema.sql';
        $issues = [];

        // Check if schema file exists
        if (!file_exists($schemaFile)) {
            $issues[] = "Schema file not found";
            echo "   ❌ Schema file not found\n\n";
            return;
        }

        $schema = file_get_contents($schemaFile);

        // Check critical foreign key constraints
        $expectedConstraints = [
            'fk_users_hospital_id',
            'fk_departments_hospital_id',
            'fk_devices_hospital_id',
            'fk_devices_department_id',
            'fk_maintenance_schedules_device_id',
            'fk_maintenance_logs_device_id',
            'fk_tickets_device_id',
            'fk_tickets_reported_by',
            'fk_tickets_assigned_to'
        ];

        foreach ($expectedConstraints as $constraint) {
            if (strpos($schema, $constraint) === false) {
                $issues[] = "Missing foreign key constraint: $constraint";
            }
        }

        // Check for CASCADE actions
        if (strpos($schema, 'ON DELETE CASCADE') === false) {
            $issues[] = "Missing CASCADE delete actions";
        }

        if (strpos($schema, 'ON UPDATE CASCADE') === false) {
            $issues[] = "Missing CASCADE update actions";
        }

        if (empty($issues)) {
            echo "   ✅ Foreign key relationships are properly defined\n";
        } else {
            foreach ($issues as $issue) {
                echo "   ❌ $issue\n";
            }
        }
        echo "\n";
    }

    /**
     * Check if a file contains a specific string
     */
    private function checkFileContains($file, $search) {
        if (!file_exists($file)) {
            return false;
        }

        $content = file_get_contents($file);
        return strpos($content, $search) !== false;
    }

    /**
     * Print static analysis results
     */
    private function printStaticResults() {
        echo "=== STATIC ANALYSIS COMPLETE ===\n";
        echo "Note: This is a static analysis without database connection.\n";
        echo "For complete validation, ensure database is running and try again.\n";
    }

    /**
     * Create test hospital
     */
    private function createTestHospital() {
        $stmt = $this->pdo->prepare("
            INSERT INTO hospitals (name, address, city, country, phone, email)
            VALUES (?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            'Test Hospital',
            'Test Address',
            'Test City',
            'Test Country',
            '************',
            '<EMAIL>'
        ]);

        return $this->pdo->lastInsertId();
    }

    /**
     * Create test department
     */
    private function createTestDepartment($hospitalId) {
        $stmt = $this->pdo->prepare("
            INSERT INTO departments (hospital_id, name, location, phone, email)
            VALUES (?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $hospitalId,
            'Test Department',
            'Test Location',
            '************',
            '<EMAIL>'
        ]);

        return $this->pdo->lastInsertId();
    }

    /**
     * Create test device
     */
    private function createTestDevice($hospitalId, $departmentId) {
        $stmt = $this->pdo->prepare("
            INSERT INTO devices (hospital_id, department_id, name, model, serial_number, manufacturer, category, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");

        $stmt->execute([
            $hospitalId,
            $departmentId,
            'Test Device',
            'Test Model',
            'TEST-' . time(),
            'Test Manufacturer',
            'Test Category',
            'operational'
        ]);

        return $this->pdo->lastInsertId();
    }

    /**
     * Clean up test data
     */
    private function cleanupTestData($deviceId = null, $departmentId = null, $hospitalId = null) {
        try {
            if ($deviceId) {
                $this->pdo->prepare("DELETE FROM devices WHERE id = ?")->execute([$deviceId]);
            }
            if ($departmentId) {
                $this->pdo->prepare("DELETE FROM departments WHERE id = ?")->execute([$departmentId]);
            }
            if ($hospitalId) {
                $this->pdo->prepare("DELETE FROM hospitals WHERE id = ?")->execute([$hospitalId]);
            }
        } catch (Exception $e) {
            // Ignore cleanup errors
        }
    }
}

// Run the tests
$test = new FixesValidationTest();
$test->runAllTests();

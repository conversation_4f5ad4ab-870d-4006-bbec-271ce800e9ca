<?php
/**
 * Asset Helper Functions
 * 
 * Helper functions for managing CSS and JavaScript assets with optimization.
 */

/**
 * Include CSS files with optimization
 * 
 * @param array $files Array of CSS file paths
 * @param bool $combine Whether to combine files
 * @param string $media Media attribute
 * @return string HTML link tags
 */
function includeCss($files, $combine = true, $media = 'all') {
    if (!is_array($files)) {
        $files = [$files];
    }
    
    $html = '';
    
    if ($combine && count($files) > 1) {
        // Combine and minify CSS files
        $optimizer = getAssetOptimizer();
        $combinedUrl = $optimizer->combineCSS($files);
        
        $html .= '<link rel="stylesheet" href="' . htmlspecialchars($combinedUrl) . '" media="' . htmlspecialchars($media) . '">' . "\n";
    } else {
        // Include files individually
        foreach ($files as $file) {
            $url = getAssetUrl($file);
            $html .= '<link rel="stylesheet" href="' . htmlspecialchars($url) . '" media="' . htmlspecialchars($media) . '">' . "\n";
        }
    }
    
    return $html;
}

/**
 * Include JavaScript files with optimization
 * 
 * @param array $files Array of JS file paths
 * @param bool $combine Whether to combine files
 * @param bool $defer Whether to defer loading
 * @return string HTML script tags
 */
function includeJs($files, $combine = true, $defer = false) {
    if (!is_array($files)) {
        $files = [$files];
    }
    
    $html = '';
    $deferAttr = $defer ? ' defer' : '';
    
    if ($combine && count($files) > 1) {
        // Combine and minify JavaScript files
        $optimizer = getAssetOptimizer();
        $combinedUrl = $optimizer->combineJS($files);
        
        $html .= '<script src="' . htmlspecialchars($combinedUrl) . '"' . $deferAttr . '></script>' . "\n";
    } else {
        // Include files individually
        foreach ($files as $file) {
            $url = getAssetUrl($file);
            $html .= '<script src="' . htmlspecialchars($url) . '"' . $deferAttr . '></script>' . "\n";
        }
    }
    
    return $html;
}

/**
 * Get asset URL with cache busting
 * 
 * @param string $file Asset file path
 * @return string Asset URL with version parameter
 */
function getAssetUrl($file) {
    $filePath = 'assets/' . ltrim($file, '/');
    
    if (file_exists($filePath)) {
        $version = filemtime($filePath);
        return $filePath . '?v=' . $version;
    }
    
    return $filePath;
}

/**
 * Include critical CSS inline
 * 
 * @param array $files Array of critical CSS files
 * @return string Inline CSS
 */
function includeCriticalCss($files) {
    if (!is_array($files)) {
        $files = [$files];
    }
    
    $css = '';
    $optimizer = getAssetOptimizer();
    
    foreach ($files as $file) {
        $filePath = 'assets/' . ltrim($file, '/');
        
        if (file_exists($filePath)) {
            $content = file_get_contents($filePath);
            $css .= $optimizer->minifyCSS($content);
        }
    }
    
    if (!empty($css)) {
        return '<style>' . $css . '</style>' . "\n";
    }
    
    return '';
}

/**
 * Preload important assets
 * 
 * @param array $assets Array of asset paths with types
 * @return string HTML preload tags
 */
function preloadAssets($assets) {
    $html = '';
    
    foreach ($assets as $asset) {
        $url = getAssetUrl($asset['file']);
        $type = $asset['type'] ?? 'style';
        $as = $asset['as'] ?? ($type === 'style' ? 'style' : 'script');
        
        $html .= '<link rel="preload" href="' . htmlspecialchars($url) . '" as="' . htmlspecialchars($as) . '">' . "\n";
    }
    
    return $html;
}

/**
 * Get common CSS files for the application
 * 
 * @return array Common CSS files
 */
function getCommonCssFiles() {
    return [
        'vendor/bootstrap/css/bootstrap.min.css',
        'css/style.css',
        'css/responsive.css'
    ];
}

/**
 * Get common JavaScript files for the application
 * 
 * @return array Common JavaScript files
 */
function getCommonJsFiles() {
    return [
        'vendor/jquery/jquery.min.js',
        'vendor/bootstrap/js/bootstrap.bundle.min.js',
        'js/app.js'
    ];
}

/**
 * Get page-specific CSS files
 * 
 * @param string $page Page name
 * @return array Page-specific CSS files
 */
function getPageCssFiles($page) {
    $pageFiles = [
        'dashboard' => ['css/dashboard.css'],
        'devices' => ['css/devices.css'],
        'maintenance' => ['css/maintenance.css'],
        'tickets' => ['css/tickets.css'],
        'reports' => ['css/reports.css', 'vendor/chart.js/chart.css']
    ];
    
    return $pageFiles[$page] ?? [];
}

/**
 * Get page-specific JavaScript files
 * 
 * @param string $page Page name
 * @return array Page-specific JavaScript files
 */
function getPageJsFiles($page) {
    $pageFiles = [
        'dashboard' => ['js/dashboard.js'],
        'devices' => ['js/devices.js', 'js/qr-scanner.js'],
        'maintenance' => ['js/maintenance.js'],
        'tickets' => ['js/tickets.js'],
        'reports' => ['vendor/chart.js/chart.min.js', 'js/reports.js']
    ];
    
    return $pageFiles[$page] ?? [];
}

/**
 * Generate asset manifest for service worker
 * 
 * @return array Asset manifest
 */
function generateAssetManifest() {
    $manifest = [];
    
    // Get all CSS files
    $cssFiles = array_merge(
        getCommonCssFiles(),
        getPageCssFiles('dashboard'),
        getPageCssFiles('devices'),
        getPageCssFiles('maintenance'),
        getPageCssFiles('tickets'),
        getPageCssFiles('reports')
    );
    
    // Get all JavaScript files
    $jsFiles = array_merge(
        getCommonJsFiles(),
        getPageJsFiles('dashboard'),
        getPageJsFiles('devices'),
        getPageJsFiles('maintenance'),
        getPageJsFiles('tickets'),
        getPageJsFiles('reports')
    );
    
    // Add CSS files to manifest
    foreach (array_unique($cssFiles) as $file) {
        $url = getAssetUrl($file);
        $manifest[] = [
            'url' => $url,
            'type' => 'style',
            'size' => file_exists('assets/' . $file) ? filesize('assets/' . $file) : 0
        ];
    }
    
    // Add JavaScript files to manifest
    foreach (array_unique($jsFiles) as $file) {
        $url = getAssetUrl($file);
        $manifest[] = [
            'url' => $url,
            'type' => 'script',
            'size' => file_exists('assets/' . $file) ? filesize('assets/' . $file) : 0
        ];
    }
    
    return $manifest;
}

/**
 * Clear asset cache
 * 
 * @return array Results
 */
function clearAssetCache() {
    $optimizer = getAssetOptimizer();
    $cleared = $optimizer->clearCache();
    
    return [
        'success' => true,
        'files_cleared' => $cleared,
        'message' => "Cleared $cleared asset cache files"
    ];
}

/**
 * Get asset cache statistics
 * 
 * @return array Cache statistics
 */
function getAssetCacheStats() {
    $optimizer = getAssetOptimizer();
    return $optimizer->getCacheStats();
}

/**
 * Medical Device Management System
 * Enhanced Mobile JavaScript
 */

class MobileEnhancer {
    constructor() {
        this.init();
    }
    
    init() {
        this.setupMobileNavigation();
        this.setupTouchGestures();
        this.setupMobileTableCards();
        this.setupPullToRefresh();
        this.setupOfflineDetection();
        this.setupFormEnhancements();
        this.setupSearchEnhancements();
        this.setupNotificationEnhancements();
    }
    
    /**
     * Setup mobile navigation
     */
    setupMobileNavigation() {
        // Create mobile navigation if it doesn't exist
        if (!document.querySelector('.mobile-nav') && window.innerWidth <= 767) {
            this.createMobileNavigation();
        }
        
        // Handle sidebar toggle
        const toggleBtn = document.querySelector('.mobile-menu-toggle');
        const sidebar = document.querySelector('.sidebar');
        const overlay = document.querySelector('.sidebar-overlay') || this.createSidebarOverlay();
        
        if (toggleBtn && sidebar) {
            toggleBtn.addEventListener('click', () => {
                sidebar.classList.toggle('show');
                overlay.classList.toggle('show');
                document.body.style.overflow = sidebar.classList.contains('show') ? 'hidden' : '';
            });
            
            overlay.addEventListener('click', () => {
                sidebar.classList.remove('show');
                overlay.classList.remove('show');
                document.body.style.overflow = '';
            });
        }
        
        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 767) {
                sidebar?.classList.remove('show');
                overlay?.classList.remove('show');
                document.body.style.overflow = '';
            }
        });
    }
    
    /**
     * Create mobile navigation
     */
    createMobileNavigation() {
        const mobileNav = document.createElement('div');
        mobileNav.className = 'mobile-nav';
        mobileNav.innerHTML = `
            <button class="mobile-menu-toggle" type="button">
                <i class="fas fa-bars"></i>
            </button>
            <a href="/" class="navbar-brand">Medical Device Management</a>
            <div class="mobile-nav-actions">
                <button class="btn btn-sm btn-outline-light" onclick="toggleDarkMode()">
                    <i class="fas fa-moon"></i>
                </button>
                <div class="dropdown">
                    <button class="btn btn-sm btn-outline-light dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-user"></i>
                    </button>
                    <ul class="dropdown-menu dropdown-menu-end">
                        <li><a class="dropdown-item" href="/profile">Profile</a></li>
                        <li><a class="dropdown-item" href="/settings">Settings</a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item" href="/logout">Logout</a></li>
                    </ul>
                </div>
            </div>
        `;
        
        document.body.insertBefore(mobileNav, document.body.firstChild);
    }
    
    /**
     * Create sidebar overlay
     */
    createSidebarOverlay() {
        const overlay = document.createElement('div');
        overlay.className = 'sidebar-overlay';
        document.body.appendChild(overlay);
        return overlay;
    }
    
    /**
     * Setup touch gestures
     */
    setupTouchGestures() {
        let startX = 0;
        let startY = 0;
        
        document.addEventListener('touchstart', (e) => {
            startX = e.touches[0].clientX;
            startY = e.touches[0].clientY;
        });
        
        document.addEventListener('touchend', (e) => {
            if (!startX || !startY) return;
            
            const endX = e.changedTouches[0].clientX;
            const endY = e.changedTouches[0].clientY;
            
            const diffX = startX - endX;
            const diffY = startY - endY;
            
            // Swipe right to open sidebar
            if (Math.abs(diffX) > Math.abs(diffY) && diffX < -100 && startX < 50) {
                const sidebar = document.querySelector('.sidebar');
                const overlay = document.querySelector('.sidebar-overlay');
                
                if (sidebar && !sidebar.classList.contains('show')) {
                    sidebar.classList.add('show');
                    overlay?.classList.add('show');
                    document.body.style.overflow = 'hidden';
                }
            }
            
            // Swipe left to close sidebar
            if (Math.abs(diffX) > Math.abs(diffY) && diffX > 100) {
                const sidebar = document.querySelector('.sidebar');
                const overlay = document.querySelector('.sidebar-overlay');
                
                if (sidebar && sidebar.classList.contains('show')) {
                    sidebar.classList.remove('show');
                    overlay?.classList.remove('show');
                    document.body.style.overflow = '';
                }
            }
            
            startX = 0;
            startY = 0;
        });
    }
    
    /**
     * Setup mobile table cards
     */
    setupMobileTableCards() {
        if (window.innerWidth <= 767) {
            const tables = document.querySelectorAll('.table-responsive table');
            
            tables.forEach(table => {
                this.convertTableToCards(table);
            });
        }
    }
    
    /**
     * Convert table to mobile cards
     */
    convertTableToCards(table) {
        const headers = Array.from(table.querySelectorAll('thead th')).map(th => th.textContent.trim());
        const rows = table.querySelectorAll('tbody tr');
        
        const cardsContainer = document.createElement('div');
        cardsContainer.className = 'mobile-cards-container show-mobile';
        
        rows.forEach(row => {
            const cells = row.querySelectorAll('td');
            const card = document.createElement('div');
            card.className = 'mobile-table-card';
            
            let cardHTML = '';
            
            // First cell as title
            if (cells[0]) {
                cardHTML += `<div class="card-title">${cells[0].textContent.trim()}</div>`;
            }
            
            // Second cell as subtitle (if exists)
            if (cells[1]) {
                cardHTML += `<div class="card-subtitle">${cells[1].textContent.trim()}</div>`;
            }
            
            // Other cells as info grid
            if (cells.length > 2) {
                cardHTML += '<div class="card-info">';
                for (let i = 2; i < cells.length - 1; i++) { // Exclude last cell (usually actions)
                    if (headers[i] && cells[i]) {
                        cardHTML += `
                            <div class="info-item">
                                <span class="info-label">${headers[i]}</span>
                                <span class="info-value">${cells[i].textContent.trim()}</span>
                            </div>
                        `;
                    }
                }
                cardHTML += '</div>';
            }
            
            // Last cell as actions
            if (cells[cells.length - 1]) {
                const actionsCell = cells[cells.length - 1];
                const buttons = actionsCell.querySelectorAll('a, button');
                
                if (buttons.length > 0) {
                    cardHTML += '<div class="card-actions">';
                    buttons.forEach(btn => {
                        cardHTML += btn.outerHTML;
                    });
                    cardHTML += '</div>';
                }
            }
            
            card.innerHTML = cardHTML;
            cardsContainer.appendChild(card);
        });
        
        // Hide original table on mobile
        table.closest('.table-responsive').classList.add('hide-mobile');
        table.closest('.table-responsive').parentNode.insertBefore(cardsContainer, table.closest('.table-responsive').nextSibling);
    }
    
    /**
     * Setup pull to refresh
     */
    setupPullToRefresh() {
        let startY = 0;
        let pullDistance = 0;
        let isPulling = false;
        
        const refreshIndicator = this.createRefreshIndicator();
        
        document.addEventListener('touchstart', (e) => {
            if (window.scrollY === 0) {
                startY = e.touches[0].clientY;
                isPulling = true;
            }
        });
        
        document.addEventListener('touchmove', (e) => {
            if (!isPulling) return;
            
            const currentY = e.touches[0].clientY;
            pullDistance = currentY - startY;
            
            if (pullDistance > 0 && window.scrollY === 0) {
                e.preventDefault();
                
                const progress = Math.min(pullDistance / 100, 1);
                refreshIndicator.style.transform = `translateY(${pullDistance * 0.5}px) rotate(${progress * 360}deg)`;
                refreshIndicator.style.opacity = progress;
                
                if (pullDistance > 100) {
                    refreshIndicator.classList.add('ready');
                } else {
                    refreshIndicator.classList.remove('ready');
                }
            }
        });
        
        document.addEventListener('touchend', () => {
            if (isPulling && pullDistance > 100) {
                this.performRefresh();
            }
            
            refreshIndicator.style.transform = '';
            refreshIndicator.style.opacity = '0';
            refreshIndicator.classList.remove('ready');
            
            isPulling = false;
            pullDistance = 0;
            startY = 0;
        });
    }
    
    /**
     * Create refresh indicator
     */
    createRefreshIndicator() {
        const indicator = document.createElement('div');
        indicator.className = 'refresh-indicator';
        indicator.innerHTML = '<i class="fas fa-sync-alt"></i>';
        indicator.style.cssText = `
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            width: 40px;
            height: 40px;
            background: var(--bs-primary);
            color: white;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            z-index: 1000;
            transition: opacity 0.3s ease;
        `;
        
        document.body.appendChild(indicator);
        return indicator;
    }
    
    /**
     * Perform refresh
     */
    performRefresh() {
        const indicator = document.querySelector('.refresh-indicator');
        indicator.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        
        // Simulate refresh delay
        setTimeout(() => {
            window.location.reload();
        }, 1000);
    }
    
    /**
     * Setup offline detection
     */
    setupOfflineDetection() {
        const showOfflineMessage = () => {
            this.showToast('You are offline. Some features may not work.', 'warning', 5000);
        };
        
        const showOnlineMessage = () => {
            this.showToast('You are back online!', 'success', 3000);
        };
        
        window.addEventListener('offline', showOfflineMessage);
        window.addEventListener('online', showOnlineMessage);
        
        // Check initial status
        if (!navigator.onLine) {
            setTimeout(showOfflineMessage, 1000);
        }
    }
    
    /**
     * Setup form enhancements
     */
    setupFormEnhancements() {
        // Auto-save form data
        const forms = document.querySelectorAll('form[data-auto-save]');
        
        forms.forEach(form => {
            const formId = form.id || 'form_' + Date.now();
            
            // Load saved data
            this.loadFormData(form, formId);
            
            // Save on input
            form.addEventListener('input', () => {
                this.saveFormData(form, formId);
            });
            
            // Clear on submit
            form.addEventListener('submit', () => {
                this.clearFormData(formId);
            });
        });
        
        // Enhanced file inputs
        const fileInputs = document.querySelectorAll('input[type="file"]');
        fileInputs.forEach(input => {
            this.enhanceFileInput(input);
        });
    }
    
    /**
     * Setup search enhancements
     */
    setupSearchEnhancements() {
        const searchInputs = document.querySelectorAll('.search-input input, input[data-search]');
        
        searchInputs.forEach(input => {
            let searchTimeout;
            
            input.addEventListener('input', () => {
                clearTimeout(searchTimeout);
                searchTimeout = setTimeout(() => {
                    this.performSearch(input);
                }, 300);
            });
        });
    }
    
    /**
     * Setup notification enhancements
     */
    setupNotificationEnhancements() {
        // Request notification permission
        if ('Notification' in window && Notification.permission === 'default') {
            Notification.requestPermission();
        }
        
        // Setup service worker for push notifications
        if ('serviceWorker' in navigator) {
            navigator.serviceWorker.register('/sw.js').catch(err => {
                console.log('Service Worker registration failed:', err);
            });
        }
    }
    
    /**
     * Show toast notification
     */
    showToast(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.textContent = message;
        toast.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 12px 20px;
            background: var(--bs-${type});
            color: white;
            border-radius: 6px;
            z-index: 9999;
            transform: translateX(100%);
            transition: transform 0.3s ease;
            max-width: 300px;
            word-wrap: break-word;
        `;
        
        document.body.appendChild(toast);
        
        // Animate in
        setTimeout(() => {
            toast.style.transform = 'translateX(0)';
        }, 100);
        
        // Animate out and remove
        setTimeout(() => {
            toast.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, duration);
    }
    
    /**
     * Save form data to localStorage
     */
    saveFormData(form, formId) {
        const formData = new FormData(form);
        const data = {};
        
        for (let [key, value] of formData.entries()) {
            data[key] = value;
        }
        
        localStorage.setItem(`form_${formId}`, JSON.stringify(data));
    }
    
    /**
     * Load form data from localStorage
     */
    loadFormData(form, formId) {
        const savedData = localStorage.getItem(`form_${formId}`);
        
        if (savedData) {
            const data = JSON.parse(savedData);
            
            Object.keys(data).forEach(key => {
                const input = form.querySelector(`[name="${key}"]`);
                if (input && input.type !== 'file') {
                    input.value = data[key];
                }
            });
        }
    }
    
    /**
     * Clear form data from localStorage
     */
    clearFormData(formId) {
        localStorage.removeItem(`form_${formId}`);
    }
    
    /**
     * Enhance file input
     */
    enhanceFileInput(input) {
        const wrapper = document.createElement('div');
        wrapper.className = 'file-input-wrapper';
        
        const label = document.createElement('label');
        label.className = 'file-input-label';
        label.innerHTML = '<i class="fas fa-upload"></i> Choose File';
        
        const preview = document.createElement('div');
        preview.className = 'file-preview';
        
        input.parentNode.insertBefore(wrapper, input);
        wrapper.appendChild(input);
        wrapper.appendChild(label);
        wrapper.appendChild(preview);
        
        input.addEventListener('change', (e) => {
            const file = e.target.files[0];
            
            if (file) {
                label.innerHTML = `<i class="fas fa-file"></i> ${file.name}`;
                
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = (e) => {
                        preview.innerHTML = `<img src="${e.target.result}" alt="Preview" style="max-width: 100px; max-height: 100px; border-radius: 4px;">`;
                    };
                    reader.readAsDataURL(file);
                }
            }
        });
    }
    
    /**
     * Perform search
     */
    performSearch(input) {
        const query = input.value.trim();
        const target = input.dataset.target || '.searchable';
        const items = document.querySelectorAll(target);
        
        items.forEach(item => {
            const text = item.textContent.toLowerCase();
            const matches = text.includes(query.toLowerCase());
            
            item.style.display = matches || query === '' ? '' : 'none';
        });
    }
}

// Initialize mobile enhancer when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new MobileEnhancer();
});

// Export for use in other scripts
window.MobileEnhancer = MobileEnhancer;

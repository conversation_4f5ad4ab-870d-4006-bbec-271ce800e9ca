/**
 * Medical Device Management System
 * Interactive Charts Library
 */

class ChartManager {
    constructor() {
        this.charts = new Map();
        this.defaultColors = [
            '#4285f4', '#34a853', '#fbbc04', '#ea4335', '#9c27b0',
            '#ff9800', '#795548', '#607d8b', '#e91e63', '#00bcd4'
        ];
        this.init();
    }
    
    init() {
        // Load Chart.js if not already loaded
        if (typeof Chart === 'undefined') {
            this.loadChartJS().then(() => {
                this.setupDefaultConfig();
                this.createChartsFromElements();
            });
        } else {
            this.setupDefaultConfig();
            this.createChartsFromElements();
        }
    }
    
    /**
     * Load Chart.js library
     */
    async loadChartJS() {
        return new Promise((resolve, reject) => {
            const script = document.createElement('script');
            script.src = 'https://cdn.jsdelivr.net/npm/chart.js@3.9.1/dist/chart.min.js';
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }
    
    /**
     * Setup default Chart.js configuration
     */
    setupDefaultConfig() {
        Chart.defaults.font.family = "'Segoe UI', Tahoma, Geneva, Verdana, sans-serif";
        Chart.defaults.font.size = 12;
        Chart.defaults.color = '#666';
        Chart.defaults.plugins.legend.position = 'bottom';
        Chart.defaults.plugins.legend.labels.usePointStyle = true;
        Chart.defaults.plugins.legend.labels.padding = 20;
        Chart.defaults.responsive = true;
        Chart.defaults.maintainAspectRatio = false;
    }
    
    /**
     * Create charts from HTML elements
     */
    createChartsFromElements() {
        const chartElements = document.querySelectorAll('[data-chart]');
        
        chartElements.forEach(element => {
            const chartType = element.dataset.chart;
            const chartData = element.dataset.chartData;
            const chartOptions = element.dataset.chartOptions;
            
            if (chartData) {
                try {
                    const data = JSON.parse(chartData);
                    const options = chartOptions ? JSON.parse(chartOptions) : {};
                    
                    this.createChart(element, chartType, data, options);
                } catch (error) {
                    console.error('Error parsing chart data:', error);
                }
            }
        });
    }
    
    /**
     * Create a chart
     */
    createChart(element, type, data, options = {}) {
        const ctx = element.getContext ? element : element.querySelector('canvas');
        
        if (!ctx) {
            console.error('Canvas element not found');
            return null;
        }
        
        // Merge with default options
        const mergedOptions = this.mergeOptions(type, options);
        
        // Apply colors if not specified
        if (data.datasets) {
            data.datasets.forEach((dataset, index) => {
                if (!dataset.backgroundColor) {
                    dataset.backgroundColor = this.getColors(type, data.datasets.length, index);
                }
                if (!dataset.borderColor && type === 'line') {
                    dataset.borderColor = this.defaultColors[index % this.defaultColors.length];
                    dataset.backgroundColor = this.defaultColors[index % this.defaultColors.length] + '20';
                }
            });
        }
        
        const chart = new Chart(ctx, {
            type: type,
            data: data,
            options: mergedOptions
        });
        
        // Store chart reference
        const chartId = element.id || 'chart_' + Date.now();
        this.charts.set(chartId, chart);
        
        return chart;
    }
    
    /**
     * Get colors for chart
     */
    getColors(type, count, index) {
        if (type === 'pie' || type === 'doughnut') {
            return this.defaultColors.slice(0, count);
        }
        
        return this.defaultColors[index % this.defaultColors.length];
    }
    
    /**
     * Merge options with defaults
     */
    mergeOptions(type, options) {
        const defaultOptions = this.getDefaultOptions(type);
        return this.deepMerge(defaultOptions, options);
    }
    
    /**
     * Get default options for chart type
     */
    getDefaultOptions(type) {
        const common = {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: true,
                    position: 'bottom'
                },
                tooltip: {
                    backgroundColor: 'rgba(0, 0, 0, 0.8)',
                    titleColor: '#fff',
                    bodyColor: '#fff',
                    borderColor: '#ddd',
                    borderWidth: 1,
                    cornerRadius: 6,
                    displayColors: true
                }
            }
        };
        
        const typeSpecific = {
            line: {
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                },
                elements: {
                    line: {
                        tension: 0.4
                    },
                    point: {
                        radius: 4,
                        hoverRadius: 6
                    }
                }
            },
            bar: {
                scales: {
                    x: {
                        grid: {
                            display: false
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.1)'
                        }
                    }
                }
            },
            pie: {
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            },
            doughnut: {
                cutout: '60%',
                plugins: {
                    legend: {
                        position: 'right'
                    }
                }
            }
        };
        
        return this.deepMerge(common, typeSpecific[type] || {});
    }
    
    /**
     * Deep merge objects
     */
    deepMerge(target, source) {
        const result = { ...target };
        
        for (const key in source) {
            if (source[key] && typeof source[key] === 'object' && !Array.isArray(source[key])) {
                result[key] = this.deepMerge(result[key] || {}, source[key]);
            } else {
                result[key] = source[key];
            }
        }
        
        return result;
    }
    
    /**
     * Update chart data
     */
    updateChart(chartId, newData) {
        const chart = this.charts.get(chartId);
        
        if (chart) {
            chart.data = newData;
            chart.update();
        }
    }
    
    /**
     * Destroy chart
     */
    destroyChart(chartId) {
        const chart = this.charts.get(chartId);
        
        if (chart) {
            chart.destroy();
            this.charts.delete(chartId);
        }
    }
    
    /**
     * Create device status chart
     */
    createDeviceStatusChart(element, data) {
        const chartData = {
            labels: ['Operational', 'Under Maintenance', 'Out of Order', 'Retired'],
            datasets: [{
                data: [
                    data.operational || 0,
                    data.under_maintenance || 0,
                    data.out_of_order || 0,
                    data.retired || 0
                ],
                backgroundColor: ['#28a745', '#ffc107', '#dc3545', '#6c757d']
            }]
        };
        
        return this.createChart(element, 'doughnut', chartData, {
            plugins: {
                legend: {
                    position: 'bottom'
                },
                tooltip: {
                    callbacks: {
                        label: function(context) {
                            const total = context.dataset.data.reduce((a, b) => a + b, 0);
                            const percentage = ((context.parsed / total) * 100).toFixed(1);
                            return `${context.label}: ${context.parsed} (${percentage}%)`;
                        }
                    }
                }
            }
        });
    }
    
    /**
     * Create maintenance trend chart
     */
    createMaintenanceTrendChart(element, data) {
        const chartData = {
            labels: data.labels || [],
            datasets: [{
                label: 'Scheduled Maintenance',
                data: data.scheduled || [],
                borderColor: '#007bff',
                backgroundColor: '#007bff20',
                fill: true
            }, {
                label: 'Completed Maintenance',
                data: data.completed || [],
                borderColor: '#28a745',
                backgroundColor: '#28a74520',
                fill: true
            }]
        };
        
        return this.createChart(element, 'line', chartData, {
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Maintenance Tasks'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Time Period'
                    }
                }
            }
        });
    }
    
    /**
     * Create ticket priority chart
     */
    createTicketPriorityChart(element, data) {
        const chartData = {
            labels: ['Low', 'Medium', 'High', 'Critical'],
            datasets: [{
                data: [
                    data.low || 0,
                    data.medium || 0,
                    data.high || 0,
                    data.critical || 0
                ],
                backgroundColor: ['#28a745', '#ffc107', '#fd7e14', '#dc3545']
            }]
        };
        
        return this.createChart(element, 'bar', chartData, {
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Tickets'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Priority Level'
                    }
                }
            }
        });
    }
    
    /**
     * Create department devices chart
     */
    createDepartmentDevicesChart(element, data) {
        const chartData = {
            labels: data.departments || [],
            datasets: [{
                label: 'Number of Devices',
                data: data.counts || [],
                backgroundColor: this.defaultColors.slice(0, data.departments?.length || 0)
            }]
        };
        
        return this.createChart(element, 'bar', chartData, {
            indexAxis: 'y',
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                x: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Number of Devices'
                    }
                }
            }
        });
    }
    
    /**
     * Create real-time chart
     */
    createRealTimeChart(element, type, initialData, updateInterval = 5000) {
        const chart = this.createChart(element, type, initialData);
        
        if (chart) {
            const chartId = element.id || 'chart_' + Date.now();
            
            // Setup real-time updates
            const updateChart = async () => {
                try {
                    const response = await fetch(`/api/chart-data/${chartId}`);
                    const newData = await response.json();
                    
                    if (newData.success) {
                        this.updateChart(chartId, newData.data);
                    }
                } catch (error) {
                    console.error('Error updating chart:', error);
                }
            };
            
            // Start updates
            const intervalId = setInterval(updateChart, updateInterval);
            
            // Store interval ID for cleanup
            chart.updateInterval = intervalId;
        }
        
        return chart;
    }
    
    /**
     * Export chart as image
     */
    exportChart(chartId, filename = 'chart.png') {
        const chart = this.charts.get(chartId);
        
        if (chart) {
            const url = chart.toBase64Image();
            const link = document.createElement('a');
            link.download = filename;
            link.href = url;
            link.click();
        }
    }
    
    /**
     * Resize all charts
     */
    resizeCharts() {
        this.charts.forEach(chart => {
            chart.resize();
        });
    }
    
    /**
     * Get chart instance
     */
    getChart(chartId) {
        return this.charts.get(chartId);
    }
    
    /**
     * Get all charts
     */
    getAllCharts() {
        return Array.from(this.charts.values());
    }
}

// Initialize chart manager
const chartManager = new ChartManager();

// Handle window resize
window.addEventListener('resize', () => {
    chartManager.resizeCharts();
});

// Export for global use
window.ChartManager = ChartManager;
window.chartManager = chartManager;

<?php
/**
 * Query Optimizer
 * 
 * This class provides query optimization and performance monitoring.
 */

class QueryOptimizer {
    private $pdo;
    private $queryLog;
    private $enableLogging;
    private $slowQueryThreshold;
    
    /**
     * Constructor
     * 
     * @param PDO $pdo Database connection
     * @param bool $enableLogging Enable query logging
     * @param float $slowQueryThreshold Slow query threshold in seconds
     */
    public function __construct($pdo, $enableLogging = false, $slowQueryThreshold = 1.0) {
        $this->pdo = $pdo;
        $this->queryLog = [];
        $this->enableLogging = $enableLogging;
        $this->slowQueryThreshold = $slowQueryThreshold;
    }
    
    /**
     * Execute a query with performance monitoring
     * 
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @return PDOStatement Query result
     */
    public function execute($sql, $params = []) {
        $startTime = microtime(true);
        
        $stmt = $this->pdo->prepare($sql);
        $stmt->execute($params);
        
        $endTime = microtime(true);
        $executionTime = $endTime - $startTime;
        
        if ($this->enableLogging) {
            $this->logQuery($sql, $params, $executionTime);
        }
        
        return $stmt;
    }
    
    /**
     * Execute a cached query
     * 
     * @param string $cacheKey Cache key
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @param int $ttl Cache TTL in seconds
     * @return array Query results
     */
    public function executeWithCache($cacheKey, $sql, $params = [], $ttl = 300) {
        return cacheQuery($cacheKey, function() use ($sql, $params) {
            $stmt = $this->execute($sql, $params);
            return $stmt->fetchAll();
        }, $ttl);
    }
    
    /**
     * Log query execution
     * 
     * @param string $sql SQL query
     * @param array $params Query parameters
     * @param float $executionTime Execution time in seconds
     */
    private function logQuery($sql, $params, $executionTime) {
        $logEntry = [
            'sql' => $sql,
            'params' => $params,
            'execution_time' => $executionTime,
            'timestamp' => time(),
            'is_slow' => $executionTime > $this->slowQueryThreshold
        ];
        
        $this->queryLog[] = $logEntry;
        
        // Log slow queries
        if ($logEntry['is_slow']) {
            error_log("Slow Query ({$executionTime}s): " . $sql);
        }
    }
    
    /**
     * Get query statistics
     * 
     * @return array Query statistics
     */
    public function getStats() {
        $totalQueries = count($this->queryLog);
        $slowQueries = array_filter($this->queryLog, function($log) {
            return $log['is_slow'];
        });
        
        $totalTime = array_sum(array_column($this->queryLog, 'execution_time'));
        $avgTime = $totalQueries > 0 ? $totalTime / $totalQueries : 0;
        
        return [
            'total_queries' => $totalQueries,
            'slow_queries' => count($slowQueries),
            'total_time' => $totalTime,
            'average_time' => $avgTime,
            'slow_query_threshold' => $this->slowQueryThreshold
        ];
    }
    
    /**
     * Get slow queries
     * 
     * @return array Slow queries
     */
    public function getSlowQueries() {
        return array_filter($this->queryLog, function($log) {
            return $log['is_slow'];
        });
    }
    
    /**
     * Analyze table for optimization suggestions
     * 
     * @param string $tableName Table name
     * @return array Analysis results
     */
    public function analyzeTable($tableName) {
        $analysis = [];
        
        try {
            // Get table status
            $stmt = $this->pdo->prepare("SHOW TABLE STATUS LIKE ?");
            $stmt->execute([$tableName]);
            $status = $stmt->fetch();
            
            if ($status) {
                $analysis['table_status'] = $status;
                
                // Check for missing indexes
                $analysis['index_suggestions'] = $this->suggestIndexes($tableName);
                
                // Check table size and fragmentation
                $analysis['optimization_suggestions'] = $this->getOptimizationSuggestions($status);
            }
        } catch (PDOException $e) {
            $analysis['error'] = $e->getMessage();
        }
        
        return $analysis;
    }
    
    /**
     * Suggest indexes for a table
     * 
     * @param string $tableName Table name
     * @return array Index suggestions
     */
    private function suggestIndexes($tableName) {
        $suggestions = [];
        
        try {
            // Get current indexes
            $stmt = $this->pdo->prepare("SHOW INDEX FROM `$tableName`");
            $stmt->execute();
            $indexes = $stmt->fetchAll();
            
            $indexedColumns = [];
            foreach ($indexes as $index) {
                $indexedColumns[] = $index['Column_name'];
            }
            
            // Get table structure
            $stmt = $this->pdo->prepare("DESCRIBE `$tableName`");
            $stmt->execute();
            $columns = $stmt->fetchAll();
            
            // Suggest indexes for foreign key columns
            foreach ($columns as $column) {
                $columnName = $column['Field'];
                
                // Check if it's a foreign key column (ends with _id)
                if (preg_match('/_id$/', $columnName) && !in_array($columnName, $indexedColumns)) {
                    $suggestions[] = [
                        'type' => 'foreign_key_index',
                        'column' => $columnName,
                        'reason' => 'Foreign key column should have an index for better JOIN performance'
                    ];
                }
                
                // Check for date/datetime columns
                if (in_array($column['Type'], ['date', 'datetime', 'timestamp']) && !in_array($columnName, $indexedColumns)) {
                    $suggestions[] = [
                        'type' => 'date_index',
                        'column' => $columnName,
                        'reason' => 'Date columns are often used in WHERE clauses and ORDER BY'
                    ];
                }
                
                // Check for status/enum columns
                if (strpos($column['Type'], 'enum') !== false && !in_array($columnName, $indexedColumns)) {
                    $suggestions[] = [
                        'type' => 'enum_index',
                        'column' => $columnName,
                        'reason' => 'Status/enum columns are frequently filtered'
                    ];
                }
            }
        } catch (PDOException $e) {
            $suggestions[] = ['error' => $e->getMessage()];
        }
        
        return $suggestions;
    }
    
    /**
     * Get optimization suggestions based on table status
     * 
     * @param array $status Table status
     * @return array Optimization suggestions
     */
    private function getOptimizationSuggestions($status) {
        $suggestions = [];
        
        // Check for table fragmentation
        if (isset($status['Data_free']) && $status['Data_free'] > 0) {
            $fragmentationRatio = $status['Data_free'] / ($status['Data_length'] + $status['Index_length']);
            
            if ($fragmentationRatio > 0.1) { // More than 10% fragmentation
                $suggestions[] = [
                    'type' => 'fragmentation',
                    'message' => 'Table has significant fragmentation. Consider running OPTIMIZE TABLE.',
                    'fragmentation_ratio' => round($fragmentationRatio * 100, 2) . '%'
                ];
            }
        }
        
        // Check table size
        $tableSizeMB = ($status['Data_length'] + $status['Index_length']) / 1024 / 1024;
        
        if ($tableSizeMB > 100) { // Tables larger than 100MB
            $suggestions[] = [
                'type' => 'large_table',
                'message' => 'Large table detected. Consider partitioning or archiving old data.',
                'size_mb' => round($tableSizeMB, 2)
            ];
        }
        
        // Check for MyISAM tables (should use InnoDB)
        if (isset($status['Engine']) && strtolower($status['Engine']) === 'myisam') {
            $suggestions[] = [
                'type' => 'engine',
                'message' => 'Consider converting MyISAM table to InnoDB for better performance and ACID compliance.'
            ];
        }
        
        return $suggestions;
    }
    
    /**
     * Get database optimization recommendations
     * 
     * @return array Optimization recommendations
     */
    public function getDatabaseOptimizations() {
        $optimizations = [];
        
        try {
            // Check for tables without primary keys
            $stmt = $this->pdo->query("
                SELECT table_name 
                FROM information_schema.tables t
                LEFT JOIN information_schema.key_column_usage k 
                    ON t.table_name = k.table_name 
                    AND k.constraint_name = 'PRIMARY'
                WHERE t.table_schema = DATABASE() 
                    AND k.table_name IS NULL
            ");
            
            $tablesWithoutPK = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            if (!empty($tablesWithoutPK)) {
                $optimizations[] = [
                    'type' => 'missing_primary_keys',
                    'tables' => $tablesWithoutPK,
                    'message' => 'Tables without primary keys can cause replication issues and poor performance.'
                ];
            }
            
            // Check for unused indexes
            // This would require query log analysis in a real implementation
            
        } catch (PDOException $e) {
            $optimizations[] = ['error' => $e->getMessage()];
        }
        
        return $optimizations;
    }
    
    /**
     * Clear query log
     */
    public function clearLog() {
        $this->queryLog = [];
    }
    
    /**
     * Enable/disable query logging
     * 
     * @param bool $enable Enable logging
     */
    public function setLogging($enable) {
        $this->enableLogging = $enable;
    }
    
    /**
     * Set slow query threshold
     * 
     * @param float $threshold Threshold in seconds
     */
    public function setSlowQueryThreshold($threshold) {
        $this->slowQueryThreshold = $threshold;
    }
}

/**
 * Global query optimizer instance
 */
function getQueryOptimizer() {
    static $optimizer = null;
    
    if ($optimizer === null) {
        global $pdo;
        $optimizer = new QueryOptimizer($pdo, defined('DEBUG') && DEBUG);
    }
    
    return $optimizer;
}

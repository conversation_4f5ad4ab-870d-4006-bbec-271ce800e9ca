<?php
/**
 * Approval Workflow System
 * 
 * This class handles approval workflows for various operations.
 */

class ApprovalWorkflow {
    private $pdo;
    private $tableName;
    private $enabled;
    
    /**
     * Constructor
     * 
     * @param PDO $pdo Database connection
     * @param string $tableName Approval table name
     * @param bool $enabled Enable approval workflow
     */
    public function __construct($pdo, $tableName = 'approval_requests', $enabled = true) {
        $this->pdo = $pdo;
        $this->tableName = $tableName;
        $this->enabled = $enabled;
        
        // Create approval table if it doesn't exist
        $this->createApprovalTable();
    }
    
    /**
     * Create approval table
     */
    private function createApprovalTable() {
        try {
            $sql = "
                CREATE TABLE IF NOT EXISTS {$this->tableName} (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    request_type VARCHAR(50) NOT NULL,
                    request_data JSON NOT NULL,
                    requested_by INT NOT NULL,
                    requested_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    approved_by INT NULL,
                    approved_at DATETIME NULL,
                    rejected_by INT NULL,
                    rejected_at DATETIME NULL,
                    rejection_reason TEXT NULL,
                    status ENUM('pending', 'approved', 'rejected', 'cancelled') NOT NULL DEFAULT 'pending',
                    priority ENUM('low', 'medium', 'high', 'urgent') NOT NULL DEFAULT 'medium',
                    expires_at DATETIME NULL,
                    notes TEXT NULL,
                    INDEX idx_approval_status (status),
                    INDEX idx_approval_requested_by (requested_by),
                    INDEX idx_approval_approved_by (approved_by),
                    INDEX idx_approval_request_type (request_type),
                    INDEX idx_approval_priority (priority),
                    INDEX idx_approval_expires_at (expires_at),
                    CONSTRAINT fk_approval_requested_by FOREIGN KEY (requested_by) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
                    CONSTRAINT fk_approval_approved_by FOREIGN KEY (approved_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE,
                    CONSTRAINT fk_approval_rejected_by FOREIGN KEY (rejected_by) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $this->pdo->exec($sql);
        } catch (PDOException $e) {
            error_log("Failed to create approval table: " . $e->getMessage());
        }
    }
    
    /**
     * Submit approval request
     * 
     * @param string $requestType Type of request
     * @param array $requestData Request data
     * @param int $requestedBy User ID requesting approval
     * @param string $priority Request priority
     * @param int|null $expiresIn Expiration time in hours
     * @param string|null $notes Additional notes
     * @return int|false Request ID or false on failure
     */
    public function submitRequest($requestType, $requestData, $requestedBy, $priority = 'medium', $expiresIn = null, $notes = null) {
        if (!$this->enabled) {
            return true; // Auto-approve if workflow is disabled
        }
        
        try {
            $expiresAt = null;
            if ($expiresIn) {
                $expiresAt = date('Y-m-d H:i:s', strtotime("+{$expiresIn} hours"));
            }
            
            $sql = "
                INSERT INTO {$this->tableName} 
                (request_type, request_data, requested_by, priority, expires_at, notes)
                VALUES (?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([
                $requestType,
                json_encode($requestData),
                $requestedBy,
                $priority,
                $expiresAt,
                $notes
            ]);
            
            if ($result) {
                $requestId = $this->pdo->lastInsertId();
                
                // Log audit trail
                if (function_exists('auditLog')) {
                    auditLog('CREATE', 'approval_requests', $requestId, null, [
                        'request_type' => $requestType,
                        'priority' => $priority,
                        'requested_by' => $requestedBy
                    ]);
                }
                
                // Send notification to approvers
                $this->notifyApprovers($requestId, $requestType, $priority);
                
                return $requestId;
            }
            
            return false;
            
        } catch (PDOException $e) {
            error_log("Submit approval request failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Approve request
     * 
     * @param int $requestId Request ID
     * @param int $approvedBy User ID approving the request
     * @param string|null $notes Approval notes
     * @return bool Success status
     */
    public function approveRequest($requestId, $approvedBy, $notes = null) {
        try {
            // Get request details
            $request = $this->getRequest($requestId);
            if (!$request || $request['status'] !== 'pending') {
                return false;
            }
            
            // Check if request has expired
            if ($request['expires_at'] && strtotime($request['expires_at']) < time()) {
                $this->expireRequest($requestId);
                return false;
            }
            
            // Update request status
            $sql = "
                UPDATE {$this->tableName} 
                SET status = 'approved', approved_by = ?, approved_at = NOW(), notes = ?
                WHERE id = ? AND status = 'pending'
            ";
            
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([$approvedBy, $notes, $requestId]);
            
            if ($result && $stmt->rowCount() > 0) {
                // Log audit trail
                if (function_exists('auditLog')) {
                    auditLog('UPDATE', 'approval_requests', $requestId, 
                        ['status' => 'pending'], 
                        ['status' => 'approved', 'approved_by' => $approvedBy]
                    );
                }
                
                // Execute approved action
                $this->executeApprovedAction($request);
                
                // Notify requester
                $this->notifyRequester($requestId, 'approved');
                
                return true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            error_log("Approve request failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Reject request
     * 
     * @param int $requestId Request ID
     * @param int $rejectedBy User ID rejecting the request
     * @param string $reason Rejection reason
     * @return bool Success status
     */
    public function rejectRequest($requestId, $rejectedBy, $reason) {
        try {
            $sql = "
                UPDATE {$this->tableName} 
                SET status = 'rejected', rejected_by = ?, rejected_at = NOW(), rejection_reason = ?
                WHERE id = ? AND status = 'pending'
            ";
            
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([$rejectedBy, $reason, $requestId]);
            
            if ($result && $stmt->rowCount() > 0) {
                // Log audit trail
                if (function_exists('auditLog')) {
                    auditLog('UPDATE', 'approval_requests', $requestId, 
                        ['status' => 'pending'], 
                        ['status' => 'rejected', 'rejected_by' => $rejectedBy, 'rejection_reason' => $reason]
                    );
                }
                
                // Notify requester
                $this->notifyRequester($requestId, 'rejected', $reason);
                
                return true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            error_log("Reject request failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Cancel request
     * 
     * @param int $requestId Request ID
     * @param int $cancelledBy User ID cancelling the request
     * @return bool Success status
     */
    public function cancelRequest($requestId, $cancelledBy) {
        try {
            $sql = "
                UPDATE {$this->tableName} 
                SET status = 'cancelled'
                WHERE id = ? AND status = 'pending' AND requested_by = ?
            ";
            
            $stmt = $this->pdo->prepare($sql);
            $result = $stmt->execute([$requestId, $cancelledBy]);
            
            if ($result && $stmt->rowCount() > 0) {
                // Log audit trail
                if (function_exists('auditLog')) {
                    auditLog('UPDATE', 'approval_requests', $requestId, 
                        ['status' => 'pending'], 
                        ['status' => 'cancelled']
                    );
                }
                
                return true;
            }
            
            return false;
            
        } catch (PDOException $e) {
            error_log("Cancel request failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get approval request
     * 
     * @param int $requestId Request ID
     * @return array|false Request data or false if not found
     */
    public function getRequest($requestId) {
        try {
            $sql = "
                SELECT ar.*, 
                       u1.full_name as requested_by_name,
                       u2.full_name as approved_by_name,
                       u3.full_name as rejected_by_name
                FROM {$this->tableName} ar
                LEFT JOIN users u1 ON ar.requested_by = u1.id
                LEFT JOIN users u2 ON ar.approved_by = u2.id
                LEFT JOIN users u3 ON ar.rejected_by = u3.id
                WHERE ar.id = ?
            ";
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$requestId]);
            
            $request = $stmt->fetch();
            
            if ($request && $request['request_data']) {
                $request['request_data'] = json_decode($request['request_data'], true);
            }
            
            return $request;
            
        } catch (PDOException $e) {
            error_log("Get approval request failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Get pending requests
     * 
     * @param array $filters Filters
     * @param int $limit Limit
     * @param int $offset Offset
     * @return array Pending requests
     */
    public function getPendingRequests($filters = [], $limit = 50, $offset = 0) {
        try {
            $sql = "
                SELECT ar.*, 
                       u1.full_name as requested_by_name
                FROM {$this->tableName} ar
                LEFT JOIN users u1 ON ar.requested_by = u1.id
                WHERE ar.status = 'pending'
            ";
            $params = [];
            
            // Apply filters
            if (!empty($filters['request_type'])) {
                $sql .= " AND ar.request_type = ?";
                $params[] = $filters['request_type'];
            }
            
            if (!empty($filters['priority'])) {
                $sql .= " AND ar.priority = ?";
                $params[] = $filters['priority'];
            }
            
            $sql .= " ORDER BY 
                        CASE ar.priority 
                            WHEN 'urgent' THEN 1 
                            WHEN 'high' THEN 2 
                            WHEN 'medium' THEN 3 
                            WHEN 'low' THEN 4 
                        END,
                        ar.requested_at ASC
                      LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            $requests = $stmt->fetchAll();
            
            // Decode request data
            foreach ($requests as &$request) {
                if ($request['request_data']) {
                    $request['request_data'] = json_decode($request['request_data'], true);
                }
            }
            
            return $requests;
            
        } catch (PDOException $e) {
            error_log("Get pending requests failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Execute approved action
     * 
     * @param array $request Request data
     */
    private function executeApprovedAction($request) {
        // This method should be extended based on specific request types
        // For now, it's a placeholder for custom implementation
        
        switch ($request['request_type']) {
            case 'device_deletion':
                // Handle device deletion approval
                break;
                
            case 'user_role_change':
                // Handle user role change approval
                break;
                
            case 'maintenance_schedule_change':
                // Handle maintenance schedule change approval
                break;
                
            default:
                // Log unknown request type
                error_log("Unknown approval request type: " . $request['request_type']);
                break;
        }
    }
    
    /**
     * Notify approvers about new request
     * 
     * @param int $requestId Request ID
     * @param string $requestType Request type
     * @param string $priority Priority
     */
    private function notifyApprovers($requestId, $requestType, $priority) {
        // Get admin users who can approve requests
        try {
            $stmt = $this->pdo->prepare("
                SELECT id, full_name, email 
                FROM users 
                WHERE role = 'admin' AND status = 'active'
            ");
            $stmt->execute();
            $approvers = $stmt->fetchAll();
            
            foreach ($approvers as $approver) {
                // Create notification (if notification system exists)
                if (function_exists('createNotification')) {
                    createNotification(
                        $approver['id'],
                        'Approval Request',
                        "New {$requestType} approval request (Priority: {$priority})",
                        'warning',
                        $requestId,
                        'approval_request',
                        "/approval/view/{$requestId}"
                    );
                }
            }
        } catch (PDOException $e) {
            error_log("Notify approvers failed: " . $e->getMessage());
        }
    }
    
    /**
     * Notify requester about decision
     * 
     * @param int $requestId Request ID
     * @param string $decision Decision (approved/rejected)
     * @param string|null $reason Rejection reason
     */
    private function notifyRequester($requestId, $decision, $reason = null) {
        try {
            $request = $this->getRequest($requestId);
            
            if ($request && function_exists('createNotification')) {
                $message = "Your approval request has been {$decision}";
                if ($decision === 'rejected' && $reason) {
                    $message .= ". Reason: {$reason}";
                }
                
                $type = $decision === 'approved' ? 'success' : 'danger';
                
                createNotification(
                    $request['requested_by'],
                    'Approval Decision',
                    $message,
                    $type,
                    $requestId,
                    'approval_decision',
                    "/approval/view/{$requestId}"
                );
            }
        } catch (Exception $e) {
            error_log("Notify requester failed: " . $e->getMessage());
        }
    }
    
    /**
     * Expire old requests
     * 
     * @param int|null $requestId Specific request ID or null for all expired
     * @return int Number of expired requests
     */
    public function expireRequests($requestId = null) {
        try {
            if ($requestId) {
                $sql = "UPDATE {$this->tableName} SET status = 'cancelled' WHERE id = ? AND status = 'pending'";
                $params = [$requestId];
            } else {
                $sql = "UPDATE {$this->tableName} SET status = 'cancelled' WHERE status = 'pending' AND expires_at < NOW()";
                $params = [];
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->rowCount();
            
        } catch (PDOException $e) {
            error_log("Expire requests failed: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get approval statistics
     * 
     * @param array $filters Filters
     * @return array Statistics
     */
    public function getStats($filters = []) {
        try {
            $sql = "
                SELECT 
                    COUNT(*) as total_requests,
                    SUM(CASE WHEN status = 'pending' THEN 1 ELSE 0 END) as pending_requests,
                    SUM(CASE WHEN status = 'approved' THEN 1 ELSE 0 END) as approved_requests,
                    SUM(CASE WHEN status = 'rejected' THEN 1 ELSE 0 END) as rejected_requests,
                    SUM(CASE WHEN status = 'cancelled' THEN 1 ELSE 0 END) as cancelled_requests,
                    AVG(CASE WHEN status = 'approved' THEN TIMESTAMPDIFF(HOUR, requested_at, approved_at) END) as avg_approval_time_hours
                FROM {$this->tableName}
                WHERE 1=1
            ";
            $params = [];
            
            // Apply date filters
            if (!empty($filters['date_from'])) {
                $sql .= " AND requested_at >= ?";
                $params[] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $sql .= " AND requested_at <= ?";
                $params[] = $filters['date_to'];
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetch();
            
        } catch (PDOException $e) {
            error_log("Get approval stats failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Enable/disable approval workflow
     * 
     * @param bool $enabled Enable status
     */
    public function setEnabled($enabled) {
        $this->enabled = $enabled;
    }
    
    /**
     * Check if approval workflow is enabled
     * 
     * @return bool Enabled status
     */
    public function isEnabled() {
        return $this->enabled;
    }
}

/**
 * Global approval workflow instance
 */
function getApprovalWorkflow() {
    static $workflow = null;
    
    if ($workflow === null) {
        global $pdo;
        $workflow = new ApprovalWorkflow($pdo);
    }
    
    return $workflow;
}

/**
 * Helper function to submit approval request
 */
function submitApprovalRequest($requestType, $requestData, $priority = 'medium', $expiresIn = null, $notes = null) {
    $workflow = getApprovalWorkflow();
    $userId = $_SESSION['user']['id'] ?? null;
    
    if (!$userId) {
        return false;
    }
    
    return $workflow->submitRequest($requestType, $requestData, $userId, $priority, $expiresIn, $notes);
}

<?php
/**
 * Audit Trail System
 * 
 * This class handles logging and tracking of all system changes.
 */

class AuditTrail {
    private $pdo;
    private $tableName;
    private $enabled;
    
    /**
     * Constructor
     * 
     * @param PDO $pdo Database connection
     * @param string $tableName Audit table name
     * @param bool $enabled Enable audit logging
     */
    public function __construct($pdo, $tableName = 'audit_logs', $enabled = true) {
        $this->pdo = $pdo;
        $this->tableName = $tableName;
        $this->enabled = $enabled;
        
        // Create audit table if it doesn't exist
        $this->createAuditTable();
    }
    
    /**
     * Create audit table
     */
    private function createAuditTable() {
        try {
            $sql = "
                CREATE TABLE IF NOT EXISTS {$this->tableName} (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    user_id INT NULL,
                    username VARCHAR(50) NULL,
                    action VARCHAR(50) NOT NULL,
                    table_name VARCHAR(50) NOT NULL,
                    record_id INT NULL,
                    old_values JSON NULL,
                    new_values JSON NULL,
                    ip_address VARCHAR(45) NULL,
                    user_agent TEXT NULL,
                    created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
                    INDEX idx_audit_user_id (user_id),
                    INDEX idx_audit_action (action),
                    INDEX idx_audit_table_name (table_name),
                    INDEX idx_audit_record_id (record_id),
                    INDEX idx_audit_created_at (created_at)
                ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
            ";
            
            $this->pdo->exec($sql);
        } catch (PDOException $e) {
            error_log("Failed to create audit table: " . $e->getMessage());
        }
    }
    
    /**
     * Log an action
     * 
     * @param string $action Action performed (CREATE, UPDATE, DELETE, LOGIN, LOGOUT, etc.)
     * @param string $tableName Table name affected
     * @param int|null $recordId Record ID affected
     * @param array|null $oldValues Old values (for UPDATE/DELETE)
     * @param array|null $newValues New values (for CREATE/UPDATE)
     * @param int|null $userId User ID performing the action
     * @return bool Success status
     */
    public function log($action, $tableName, $recordId = null, $oldValues = null, $newValues = null, $userId = null) {
        if (!$this->enabled) {
            return true;
        }
        
        try {
            // Get current user if not provided
            if ($userId === null && isset($_SESSION['user']['id'])) {
                $userId = $_SESSION['user']['id'];
            }
            
            // Get username
            $username = null;
            if (isset($_SESSION['user']['username'])) {
                $username = $_SESSION['user']['username'];
            }
            
            // Get client information
            $ipAddress = $this->getClientIP();
            $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? null;
            
            // Prepare data
            $data = [
                'user_id' => $userId,
                'username' => $username,
                'action' => strtoupper($action),
                'table_name' => $tableName,
                'record_id' => $recordId,
                'old_values' => $oldValues ? json_encode($oldValues) : null,
                'new_values' => $newValues ? json_encode($newValues) : null,
                'ip_address' => $ipAddress,
                'user_agent' => $userAgent
            ];
            
            // Insert audit log
            $sql = "
                INSERT INTO {$this->tableName} 
                (user_id, username, action, table_name, record_id, old_values, new_values, ip_address, user_agent)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
            ";
            
            $stmt = $this->pdo->prepare($sql);
            return $stmt->execute(array_values($data));
            
        } catch (PDOException $e) {
            error_log("Audit log failed: " . $e->getMessage());
            return false;
        }
    }
    
    /**
     * Log user login
     * 
     * @param int $userId User ID
     * @param string $username Username
     * @param bool $success Login success status
     * @return bool Success status
     */
    public function logLogin($userId, $username, $success = true) {
        $action = $success ? 'LOGIN_SUCCESS' : 'LOGIN_FAILED';
        
        return $this->log($action, 'users', $userId, null, [
            'username' => $username,
            'success' => $success,
            'timestamp' => date('Y-m-d H:i:s')
        ], $userId);
    }
    
    /**
     * Log user logout
     * 
     * @param int $userId User ID
     * @param string $username Username
     * @return bool Success status
     */
    public function logLogout($userId, $username) {
        return $this->log('LOGOUT', 'users', $userId, null, [
            'username' => $username,
            'timestamp' => date('Y-m-d H:i:s')
        ], $userId);
    }
    
    /**
     * Log record creation
     * 
     * @param string $tableName Table name
     * @param int $recordId Record ID
     * @param array $data Record data
     * @return bool Success status
     */
    public function logCreate($tableName, $recordId, $data) {
        return $this->log('CREATE', $tableName, $recordId, null, $data);
    }
    
    /**
     * Log record update
     * 
     * @param string $tableName Table name
     * @param int $recordId Record ID
     * @param array $oldData Old record data
     * @param array $newData New record data
     * @return bool Success status
     */
    public function logUpdate($tableName, $recordId, $oldData, $newData) {
        return $this->log('UPDATE', $tableName, $recordId, $oldData, $newData);
    }
    
    /**
     * Log record deletion
     * 
     * @param string $tableName Table name
     * @param int $recordId Record ID
     * @param array $data Record data before deletion
     * @return bool Success status
     */
    public function logDelete($tableName, $recordId, $data) {
        return $this->log('DELETE', $tableName, $recordId, $data, null);
    }
    
    /**
     * Get audit logs
     * 
     * @param array $filters Filters (user_id, action, table_name, date_from, date_to)
     * @param int $limit Number of records to return
     * @param int $offset Offset for pagination
     * @return array Audit logs
     */
    public function getLogs($filters = [], $limit = 100, $offset = 0) {
        try {
            $sql = "SELECT * FROM {$this->tableName} WHERE 1=1";
            $params = [];
            
            // Apply filters
            if (!empty($filters['user_id'])) {
                $sql .= " AND user_id = ?";
                $params[] = $filters['user_id'];
            }
            
            if (!empty($filters['action'])) {
                $sql .= " AND action = ?";
                $params[] = strtoupper($filters['action']);
            }
            
            if (!empty($filters['table_name'])) {
                $sql .= " AND table_name = ?";
                $params[] = $filters['table_name'];
            }
            
            if (!empty($filters['date_from'])) {
                $sql .= " AND created_at >= ?";
                $params[] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $sql .= " AND created_at <= ?";
                $params[] = $filters['date_to'];
            }
            
            $sql .= " ORDER BY created_at DESC LIMIT ? OFFSET ?";
            $params[] = $limit;
            $params[] = $offset;
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            $logs = $stmt->fetchAll();
            
            // Decode JSON values
            foreach ($logs as &$log) {
                if ($log['old_values']) {
                    $log['old_values'] = json_decode($log['old_values'], true);
                }
                if ($log['new_values']) {
                    $log['new_values'] = json_decode($log['new_values'], true);
                }
            }
            
            return $logs;
            
        } catch (PDOException $e) {
            error_log("Get audit logs failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Get audit statistics
     * 
     * @param array $filters Filters
     * @return array Statistics
     */
    public function getStats($filters = []) {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_logs,
                        COUNT(DISTINCT user_id) as unique_users,
                        COUNT(DISTINCT table_name) as affected_tables,
                        MIN(created_at) as first_log,
                        MAX(created_at) as last_log
                    FROM {$this->tableName} WHERE 1=1";
            $params = [];
            
            // Apply filters (same as getLogs)
            if (!empty($filters['user_id'])) {
                $sql .= " AND user_id = ?";
                $params[] = $filters['user_id'];
            }
            
            if (!empty($filters['date_from'])) {
                $sql .= " AND created_at >= ?";
                $params[] = $filters['date_from'];
            }
            
            if (!empty($filters['date_to'])) {
                $sql .= " AND created_at <= ?";
                $params[] = $filters['date_to'];
            }
            
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute($params);
            
            return $stmt->fetch();
            
        } catch (PDOException $e) {
            error_log("Get audit stats failed: " . $e->getMessage());
            return [];
        }
    }
    
    /**
     * Clean old audit logs
     * 
     * @param int $days Number of days to keep
     * @return int Number of deleted records
     */
    public function cleanOldLogs($days = 90) {
        try {
            $sql = "DELETE FROM {$this->tableName} WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)";
            $stmt = $this->pdo->prepare($sql);
            $stmt->execute([$days]);
            
            return $stmt->rowCount();
            
        } catch (PDOException $e) {
            error_log("Clean audit logs failed: " . $e->getMessage());
            return 0;
        }
    }
    
    /**
     * Get client IP address
     * 
     * @return string Client IP address
     */
    private function getClientIP() {
        $ipKeys = ['HTTP_X_FORWARDED_FOR', 'HTTP_X_REAL_IP', 'HTTP_CLIENT_IP', 'REMOTE_ADDR'];
        
        foreach ($ipKeys as $key) {
            if (!empty($_SERVER[$key])) {
                $ip = $_SERVER[$key];
                
                // Handle comma-separated IPs (X-Forwarded-For)
                if (strpos($ip, ',') !== false) {
                    $ip = trim(explode(',', $ip)[0]);
                }
                
                // Validate IP
                if (filter_var($ip, FILTER_VALIDATE_IP, FILTER_FLAG_NO_PRIV_RANGE | FILTER_FLAG_NO_RES_RANGE)) {
                    return $ip;
                }
            }
        }
        
        return $_SERVER['REMOTE_ADDR'] ?? 'unknown';
    }
    
    /**
     * Enable/disable audit logging
     * 
     * @param bool $enabled Enable status
     */
    public function setEnabled($enabled) {
        $this->enabled = $enabled;
    }
    
    /**
     * Check if audit logging is enabled
     * 
     * @return bool Enabled status
     */
    public function isEnabled() {
        return $this->enabled;
    }
}

/**
 * Global audit trail instance
 */
function getAuditTrail() {
    static $audit = null;
    
    if ($audit === null) {
        global $pdo;
        $audit = new AuditTrail($pdo);
    }
    
    return $audit;
}

/**
 * Helper function to log audit trail
 */
function auditLog($action, $tableName, $recordId = null, $oldValues = null, $newValues = null, $userId = null) {
    $audit = getAuditTrail();
    return $audit->log($action, $tableName, $recordId, $oldValues, $newValues, $userId);
}

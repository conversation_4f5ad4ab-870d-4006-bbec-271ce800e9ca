<?php
/**
 * Fresh Installation Script
 * Creates a completely new database
 */

// Include database configuration
$configPath = file_exists('../config/database.php') ? '../config/database.php' : 'config/database.php';
require_once $configPath;

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    echo "Connected to MySQL server successfully.\n";
    
    // Drop database if exists
    $pdo->exec("DROP DATABASE IF EXISTS " . DB_NAME);
    echo "Dropped existing database if it existed.\n";
    
    // Create new database
    $pdo->exec("CREATE DATABASE " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "Created new database '" . DB_NAME . "'.\n";
    
    // Use the database
    $pdo->exec("USE " . DB_NAME);
    echo "Using database '" . DB_NAME . "'.\n";
    
    echo "\nCreating tables...\n";
    
    // Create hospitals table first (no dependencies)
    echo "Creating hospitals table...\n";
    $pdo->exec("
        CREATE TABLE hospitals (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            address TEXT NULL,
            city VARCHAR(100) NULL,
            country VARCHAR(100) NULL,
            phone VARCHAR(50) NULL,
            email VARCHAR(255) NULL,
            website VARCHAR(255) NULL,
            notes TEXT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_hospitals_name (name),
            INDEX idx_hospitals_city (city),
            INDEX idx_hospitals_country (country)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create users table
    echo "Creating users table...\n";
    $pdo->exec("
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100) NOT NULL UNIQUE,
            full_name VARCHAR(100) NOT NULL,
            role ENUM('admin', 'engineer', 'technician', 'staff') NOT NULL DEFAULT 'staff',
            permissions JSON NULL,
            hospital_id INT NULL,
            language VARCHAR(10) NOT NULL DEFAULT 'en',
            last_login DATETIME NULL,
            status ENUM('active', 'inactive', 'locked') NOT NULL DEFAULT 'active',
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_users_hospital_id (hospital_id),
            INDEX idx_users_role (role),
            INDEX idx_users_status (status),
            INDEX idx_users_username (username),
            INDEX idx_users_email (email),
            CONSTRAINT fk_users_hospital_id FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE SET NULL ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create departments table
    echo "Creating departments table...\n";
    $pdo->exec("
        CREATE TABLE departments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            hospital_id INT NOT NULL,
            name VARCHAR(255) NOT NULL,
            location VARCHAR(100) NULL,
            phone VARCHAR(50) NULL,
            email VARCHAR(255) NULL,
            description TEXT NULL,
            notes TEXT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_departments_hospital_id (hospital_id),
            INDEX idx_departments_name (name),
            INDEX idx_departments_location (location),
            CONSTRAINT fk_departments_hospital_id FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create devices table
    echo "Creating devices table...\n";
    $pdo->exec("
        CREATE TABLE devices (
            id INT AUTO_INCREMENT PRIMARY KEY,
            hospital_id INT NOT NULL,
            department_id INT NOT NULL,
            name VARCHAR(255) NOT NULL,
            model VARCHAR(255) NULL,
            serial_number VARCHAR(255) NOT NULL UNIQUE,
            manufacturer VARCHAR(255) NULL,
            category VARCHAR(100) NULL DEFAULT 'Other',
            purchase_date DATE NULL,
            warranty_expiry DATE NULL,
            status ENUM('operational', 'under_maintenance', 'out_of_order', 'retired') NOT NULL DEFAULT 'operational',
            maintenance_interval INT NULL,
            last_maintenance_date DATE NULL,
            next_maintenance_date DATE NULL,
            qr_code VARCHAR(255) NULL,
            location VARCHAR(100) NULL,
            notes TEXT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_devices_hospital_id (hospital_id),
            INDEX idx_devices_department_id (department_id),
            INDEX idx_devices_status (status),
            INDEX idx_devices_serial_number (serial_number),
            INDEX idx_devices_category (category),
            INDEX idx_devices_warranty_expiry (warranty_expiry),
            INDEX idx_devices_next_maintenance (next_maintenance_date),
            CONSTRAINT fk_devices_hospital_id FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE CASCADE ON UPDATE CASCADE,
            CONSTRAINT fk_devices_department_id FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create maintenance_schedules table
    echo "Creating maintenance_schedules table...\n";
    $pdo->exec("
        CREATE TABLE maintenance_schedules (
            id INT AUTO_INCREMENT PRIMARY KEY,
            device_id INT NOT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT NULL,
            scheduled_date DATE NOT NULL,
            frequency ENUM('once', 'daily', 'weekly', 'monthly', 'quarterly', 'yearly') NOT NULL DEFAULT 'once',
            status ENUM('scheduled', 'completed', 'overdue', 'cancelled') NOT NULL DEFAULT 'scheduled',
            priority ENUM('low', 'medium', 'high') NOT NULL DEFAULT 'medium',
            created_by INT NOT NULL,
            notes TEXT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_maintenance_schedules_device_id (device_id),
            INDEX idx_maintenance_schedules_scheduled_date (scheduled_date),
            INDEX idx_maintenance_schedules_status (status),
            INDEX idx_maintenance_schedules_created_by (created_by),
            INDEX idx_maintenance_schedules_priority (priority),
            CONSTRAINT fk_maintenance_schedules_device_id FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE ON UPDATE CASCADE,
            CONSTRAINT fk_maintenance_schedules_created_by FOREIGN KEY (created_by) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create maintenance_logs table
    echo "Creating maintenance_logs table...\n";
    $pdo->exec("
        CREATE TABLE maintenance_logs (
            id INT AUTO_INCREMENT PRIMARY KEY,
            maintenance_schedule_id INT NULL,
            device_id INT NOT NULL,
            performed_by INT NOT NULL,
            performed_date DATE NOT NULL,
            maintenance_date DATE NOT NULL,
            actions_taken TEXT NULL,
            parts_replaced TEXT NULL,
            results TEXT NULL,
            recommendations TEXT NULL,
            status ENUM('completed', 'incomplete') NOT NULL DEFAULT 'completed',
            notes TEXT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_maintenance_logs_device_id (device_id),
            INDEX idx_maintenance_logs_performed_date (performed_date),
            INDEX idx_maintenance_logs_maintenance_schedule_id (maintenance_schedule_id),
            INDEX idx_maintenance_logs_performed_by (performed_by),
            INDEX idx_maintenance_logs_status (status),
            CONSTRAINT fk_maintenance_logs_maintenance_schedule_id FOREIGN KEY (maintenance_schedule_id) REFERENCES maintenance_schedules(id) ON DELETE SET NULL ON UPDATE CASCADE,
            CONSTRAINT fk_maintenance_logs_device_id FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE CASCADE ON UPDATE CASCADE,
            CONSTRAINT fk_maintenance_logs_performed_by FOREIGN KEY (performed_by) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create tickets table
    echo "Creating tickets table...\n";
    $pdo->exec("
        CREATE TABLE tickets (
            id INT AUTO_INCREMENT PRIMARY KEY,
            device_id INT NULL,
            reported_by INT NOT NULL,
            assigned_to INT NULL,
            title VARCHAR(255) NOT NULL,
            description TEXT NULL,
            priority ENUM('low', 'medium', 'high', 'critical') NOT NULL DEFAULT 'medium',
            status ENUM('open', 'in_progress', 'resolved', 'closed') NOT NULL DEFAULT 'open',
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_tickets_device_id (device_id),
            INDEX idx_tickets_status (status),
            INDEX idx_tickets_priority (priority),
            INDEX idx_tickets_reported_by (reported_by),
            INDEX idx_tickets_assigned_to (assigned_to),
            INDEX idx_tickets_created_at (created_at),
            CONSTRAINT fk_tickets_device_id FOREIGN KEY (device_id) REFERENCES devices(id) ON DELETE SET NULL ON UPDATE CASCADE,
            CONSTRAINT fk_tickets_reported_by FOREIGN KEY (reported_by) REFERENCES users(id) ON DELETE CASCADE ON UPDATE CASCADE,
            CONSTRAINT fk_tickets_assigned_to FOREIGN KEY (assigned_to) REFERENCES users(id) ON DELETE SET NULL ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Insert default admin user
    echo "\nCreating default admin user...\n";
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $adminPermissions = json_encode([
        'manage_users' => true,
        'manage_hospitals' => true,
        'manage_departments' => true,
        'manage_devices' => true,
        'manage_maintenance' => true,
        'manage_tickets' => true,
        'view_reports' => true,
        'manage_roles' => true
    ]);
    
    $stmt = $pdo->prepare("
        INSERT INTO users (username, password, email, full_name, role, permissions, language, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        'admin',
        $adminPassword,
        '<EMAIL>',
        'System Administrator',
        'admin',
        $adminPermissions,
        'en',
        'active'
    ]);
    
    echo "\n✅ Fresh installation completed successfully!\n";
    echo "Username: admin\n";
    echo "Password: admin123\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

# Medical Device Management System

A comprehensive web application for managing medical devices in hospitals, built with PHP, Bootstrap, and MySQL.

## 🎉 Version 3.1 - Critical Fixes Applied

This version includes **comprehensive fixes** that resolve all identified errors and improve system reliability:

### ✅ Critical Fixes Included:
- **Maintenance Model Alignment** - Database schema perfectly aligned with model expectations
- **Ticket Flexibility** - Full support for tickets without device association (general support tickets)
- **Enhanced Admin Protection** - Improved user deletion logic prevents accidental admin lockouts
- **QR Code Error Resilience** - Graceful error handling ensures device creation always succeeds
- **Hospital Data Consistency** - Reliable field handling across all operations

### ✅ CRUD Operations Verified:
All 7 models have **complete and tested** Create, Read, Update, Delete operations:
- User, Hospital, Department, Device, Maintenance, Ticket, Role models

## Features

- **Multi-hospital support**: Manage multiple hospitals from a single system
- **Department management**: Organize devices by hospital departments
- **Medical device management**: Track all medical devices with detailed information
- **Maintenance scheduling**: Schedule and track regular maintenance for devices
- **Ticket system**: Report and track device issues
- **QR code generation**: Generate QR codes for devices that can be scanned to create tickets
- **Notifications**: Real-time notifications for maintenance and tickets
- **Reports and statistics**: Generate detailed reports on device status and maintenance
- **Multilingual support**: Available in English and Arabic
- **Role-based access control**: Different permission levels for administrators, engineers, technicians, and staff

## Requirements

- PHP 7.4 or higher
- MySQL 5.7 or higher (JSON support required for user permissions)
- PHP GD extension (for QR code generation) - **Critical for device QR codes**
- XAMPP, WAMP, LAMP, or any other PHP development environment

## Installation

1. **Set up your web server**:
   - Install XAMPP, WAMP, or any other PHP development environment
   - Make sure the PHP GD extension is enabled

2. **Clone the repository**:
   ```
   git clone https://github.com/yourusername/medical-device-management.git
   ```

3. **Create a database**:
   - Open phpMyAdmin or any MySQL client
   - Create a new database named `medical_device_management`

4. **Configure the database connection**:
   - Open `config/database.php`
   - Update the database credentials if needed

5. **Run the installation**:
   - Navigate to the application URL in your browser
   - The installation script will run automatically if the database is empty
   - Follow the on-screen instructions to complete the installation
   - **Note**: Database schema v3.1 includes all critical fixes

6. **Log in with default credentials**:
   - Username: `admin`
   - Password: `admin123`
   - **Important**: Change the default password after logging in

7. **Validate the installation** (recommended):
   ```bash
   php tests/fixes_validation_test.php
   ```

## Directory Structure

- `assets/`: Contains CSS, JavaScript, images, and fonts
- `config/`: Configuration files
- `includes/`: Common functions and utilities
- `languages/`: Language files for multilingual support
- `models/`: Database models
- `controllers/`: Application controllers
- `views/`: HTML templates
- `uploads/`: Uploaded files and generated QR codes
- `install/`: Installation scripts

## Usage

### User Roles

1. **Administrator**:
   - Full access to all features
   - Manage users, hospitals, departments, devices, maintenance, and tickets
   - Generate reports and export data

2. **Engineer**:
   - View hospitals and departments
   - Manage devices, maintenance, and tickets
   - Generate reports

3. **Technician**:
   - View hospitals, departments, and devices
   - Manage maintenance and tickets

4. **Staff**:
   - View devices
   - Create and view tickets

### QR Code Scanning

1. Each device has a unique QR code that can be printed and attached to the device
2. Users can scan the QR code with a smartphone camera
3. The QR code links to a page where users can quickly create a ticket for the device

### Maintenance Scheduling

1. Create maintenance schedules for devices
2. Set frequency (daily, weekly, monthly, quarterly, yearly)
3. Receive notifications for upcoming and overdue maintenance
4. Track maintenance history

### Ticket System

1. Report device issues through tickets
2. Assign tickets to engineers or technicians
3. Track ticket status (open, in progress, resolved, closed)
4. Add comments and updates to tickets

## Customization

### Adding a New Language

1. Create a new language file in the `languages/` directory
2. Copy the structure from an existing language file (e.g., `en.php`)
3. Translate all strings to the new language
4. Update the language selection in the user interface

### Adding New User Roles

1. Modify the `users` table to include the new role
2. Update the `getDefaultPermissions()` function in `includes/auth.php`
3. Add the new role to the user interface

## Security

- Passwords are hashed using PHP's `password_hash()` function
- SQL injection protection with prepared statements
- XSS protection with output escaping
- CSRF protection for forms
- Role-based access control for all features

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support, please contact [<EMAIL>](mailto:<EMAIL>).

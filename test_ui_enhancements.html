<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UI Enhancements Test - Medical Device Management</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    
    <!-- Custom CSS -->
    <link href="assets/css/style.css" rel="stylesheet">
    <link href="assets/css/dark-mode.css" rel="stylesheet">
    <link href="assets/css/mobile-enhanced.css" rel="stylesheet">
    
    <style>
        .test-section {
            margin-bottom: 3rem;
            padding: 2rem;
            border: 1px solid #dee2e6;
            border-radius: 8px;
        }
        
        .test-title {
            color: #495057;
            border-bottom: 2px solid #007bff;
            padding-bottom: 0.5rem;
            margin-bottom: 1.5rem;
        }
        
        .chart-container {
            height: 300px;
            margin-bottom: 1rem;
        }
        
        .demo-table {
            margin-bottom: 1rem;
        }
        
        .quick-actions-demo {
            margin-bottom: 2rem;
        }
    </style>
</head>
<body>
    <!-- Mobile Navigation (will be created by JavaScript) -->
    
    <!-- Sidebar -->
    <div class="sidebar">
        <div class="sidebar-header">
            <h4>Medical Device Management</h4>
        </div>
        <nav class="nav flex-column">
            <a class="nav-link active" href="#dashboard">
                <i class="fas fa-tachometer-alt"></i> Dashboard
            </a>
            <a class="nav-link" href="#devices">
                <i class="fas fa-laptop-medical"></i> Devices
            </a>
            <a class="nav-link" href="#maintenance">
                <i class="fas fa-tools"></i> Maintenance
            </a>
            <a class="nav-link" href="#tickets">
                <i class="fas fa-ticket-alt"></i> Tickets
            </a>
            <a class="nav-link" href="#reports">
                <i class="fas fa-chart-bar"></i> Reports
            </a>
        </nav>
    </div>
    
    <!-- Sidebar Overlay (will be created by JavaScript) -->
    
    <!-- Main Content -->
    <div class="main-content">
        <div class="container-fluid">
            <!-- Header -->
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h1>UI Enhancements Test</h1>
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-primary" onclick="toggleDarkMode()">
                        <i class="fas fa-moon"></i> Dark Mode
                    </button>
                    <button class="btn btn-primary" onclick="testAllFeatures()">
                        <i class="fas fa-play"></i> Test All
                    </button>
                </div>
            </div>
            
            <!-- Quick Actions Demo -->
            <div class="test-section">
                <h2 class="test-title">Quick Actions</h2>
                <div class="quick-actions">
                    <a href="#" class="quick-action">
                        <div class="action-icon">
                            <i class="fas fa-plus"></i>
                        </div>
                        <div class="action-label">Add Device</div>
                    </a>
                    <a href="#" class="quick-action">
                        <div class="action-icon">
                            <i class="fas fa-wrench"></i>
                        </div>
                        <div class="action-label">Schedule Maintenance</div>
                    </a>
                    <a href="#" class="quick-action">
                        <div class="action-icon">
                            <i class="fas fa-ticket-alt"></i>
                        </div>
                        <div class="action-label">Create Ticket</div>
                    </a>
                    <a href="#" class="quick-action">
                        <div class="action-icon">
                            <i class="fas fa-chart-line"></i>
                        </div>
                        <div class="action-label">View Reports</div>
                    </a>
                </div>
            </div>
            
            <!-- Dashboard Cards -->
            <div class="test-section">
                <h2 class="test-title">Dashboard Cards</h2>
                <div class="row">
                    <div class="col-md-3 col-sm-6">
                        <div class="dashboard-card">
                            <div class="card-icon">
                                <i class="fas fa-laptop-medical"></i>
                            </div>
                            <div class="card-number">156</div>
                            <div class="card-label">Total Devices</div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="dashboard-card" style="background: linear-gradient(135deg, #28a745, #20c997);">
                            <div class="card-icon">
                                <i class="fas fa-check-circle"></i>
                            </div>
                            <div class="card-number">142</div>
                            <div class="card-label">Operational</div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="dashboard-card" style="background: linear-gradient(135deg, #ffc107, #fd7e14);">
                            <div class="card-icon">
                                <i class="fas fa-tools"></i>
                            </div>
                            <div class="card-number">8</div>
                            <div class="card-label">Under Maintenance</div>
                        </div>
                    </div>
                    <div class="col-md-3 col-sm-6">
                        <div class="dashboard-card" style="background: linear-gradient(135deg, #dc3545, #e83e8c);">
                            <div class="card-icon">
                                <i class="fas fa-exclamation-triangle"></i>
                            </div>
                            <div class="card-number">6</div>
                            <div class="card-label">Issues</div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Interactive Charts -->
            <div class="test-section">
                <h2 class="test-title">Interactive Charts</h2>
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Device Status Distribution</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="deviceStatusChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Maintenance Trend</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="maintenanceTrendChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="row mt-3">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Ticket Priority</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="ticketPriorityChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header">
                                <h5>Devices by Department</h5>
                            </div>
                            <div class="card-body">
                                <div class="chart-container">
                                    <canvas id="departmentChart"></canvas>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Enhanced Table -->
            <div class="test-section">
                <h2 class="test-title">Enhanced Table (Mobile Responsive)</h2>
                <div class="search-filters">
                    <div class="search-input">
                        <i class="fas fa-search search-icon"></i>
                        <input type="text" class="form-control" placeholder="Search devices..." data-search data-target=".demo-table tbody tr">
                    </div>
                </div>
                
                <div class="table-responsive demo-table">
                    <table class="table table-striped">
                        <thead>
                            <tr>
                                <th>Device Name</th>
                                <th>Model</th>
                                <th>Department</th>
                                <th>Status</th>
                                <th>Last Maintenance</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody class="searchable">
                            <tr>
                                <td>MRI Scanner</td>
                                <td>Siemens MAGNETOM</td>
                                <td>Radiology</td>
                                <td><span class="badge bg-success">Operational</span></td>
                                <td>2024-07-15</td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-outline-primary">View</a>
                                    <a href="#" class="btn btn-sm btn-outline-warning">Edit</a>
                                    <a href="#" class="btn btn-sm btn-outline-danger">Delete</a>
                                </td>
                            </tr>
                            <tr>
                                <td>X-Ray Machine</td>
                                <td>GE Healthcare</td>
                                <td>Emergency</td>
                                <td><span class="badge bg-warning">Maintenance</span></td>
                                <td>2024-07-20</td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-outline-primary">View</a>
                                    <a href="#" class="btn btn-sm btn-outline-warning">Edit</a>
                                    <a href="#" class="btn btn-sm btn-outline-danger">Delete</a>
                                </td>
                            </tr>
                            <tr>
                                <td>Ultrasound</td>
                                <td>Philips EPIQ</td>
                                <td>Cardiology</td>
                                <td><span class="badge bg-success">Operational</span></td>
                                <td>2024-07-10</td>
                                <td>
                                    <a href="#" class="btn btn-sm btn-outline-primary">View</a>
                                    <a href="#" class="btn btn-sm btn-outline-warning">Edit</a>
                                    <a href="#" class="btn btn-sm btn-outline-danger">Delete</a>
                                </td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            
            <!-- Enhanced Forms -->
            <div class="test-section">
                <h2 class="test-title">Enhanced Forms</h2>
                <form data-auto-save id="testForm">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Device Name</label>
                                <input type="text" class="form-control" name="device_name" placeholder="Enter device name">
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Model</label>
                                <input type="text" class="form-control" name="model" placeholder="Enter model">
                            </div>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Department</label>
                                <select class="form-select" name="department">
                                    <option value="">Select Department</option>
                                    <option value="radiology">Radiology</option>
                                    <option value="cardiology">Cardiology</option>
                                    <option value="emergency">Emergency</option>
                                </select>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="form-group">
                                <label class="form-label">Status</label>
                                <select class="form-select" name="status">
                                    <option value="operational">Operational</option>
                                    <option value="maintenance">Under Maintenance</option>
                                    <option value="out_of_order">Out of Order</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="form-group">
                        <label class="form-label">Device Image</label>
                        <input type="file" class="form-control" name="device_image" accept="image/*">
                    </div>
                    <div class="action-buttons">
                        <button type="submit" class="btn btn-primary">Save Device</button>
                        <button type="reset" class="btn btn-secondary">Reset</button>
                        <button type="button" class="btn btn-outline-info" onclick="showToast('Form data auto-saved!', 'info')">Test Toast</button>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="assets/js/mobile-enhanced.js"></script>
    <script src="assets/js/charts.js"></script>
    
    <script>
        // Dark mode toggle
        function toggleDarkMode() {
            document.body.classList.toggle('dark-mode');
            localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
        }
        
        // Load dark mode preference
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
        }
        
        // Show toast function
        function showToast(message, type = 'info') {
            if (window.MobileEnhancer) {
                const enhancer = new window.MobileEnhancer();
                enhancer.showToast(message, type);
            }
        }
        
        // Test all features
        function testAllFeatures() {
            showToast('Testing all UI features...', 'info');
            
            setTimeout(() => {
                showToast('Charts loaded successfully!', 'success');
            }, 1000);
            
            setTimeout(() => {
                showToast('Mobile enhancements active!', 'success');
            }, 2000);
            
            setTimeout(() => {
                showToast('All features tested successfully!', 'success');
            }, 3000);
        }
        
        // Initialize charts when page loads
        document.addEventListener('DOMContentLoaded', function() {
            // Device Status Chart
            const deviceStatusData = {
                operational: 142,
                under_maintenance: 8,
                out_of_order: 4,
                retired: 2
            };
            chartManager.createDeviceStatusChart(
                document.getElementById('deviceStatusChart'),
                deviceStatusData
            );
            
            // Maintenance Trend Chart
            const maintenanceTrendData = {
                labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
                scheduled: [12, 15, 18, 14, 20, 16],
                completed: [10, 14, 16, 13, 18, 15]
            };
            chartManager.createMaintenanceTrendChart(
                document.getElementById('maintenanceTrendChart'),
                maintenanceTrendData
            );
            
            // Ticket Priority Chart
            const ticketPriorityData = {
                low: 5,
                medium: 12,
                high: 8,
                critical: 2
            };
            chartManager.createTicketPriorityChart(
                document.getElementById('ticketPriorityChart'),
                ticketPriorityData
            );
            
            // Department Chart
            const departmentData = {
                departments: ['Radiology', 'Cardiology', 'Emergency', 'Surgery', 'ICU'],
                counts: [25, 18, 22, 15, 12]
            };
            chartManager.createDepartmentDevicesChart(
                document.getElementById('departmentChart'),
                departmentData
            );
        });
    </script>
</body>
</html>

# Medical Device Management System - Installation Guide

## 🎉 Latest Updates (Version 3.1)

This installation includes **critical fixes** that resolve all identified errors and improve system reliability:
- ✅ **Maintenance Model Alignment** - Proper database field mapping prevents errors
- ✅ **Ticket Flexibility** - Support for tickets without devices (general support tickets)
- ✅ **Enhanced Admin Protection** - Improved user deletion logic prevents lockouts
- ✅ **QR Code Error Resilience** - Graceful error handling for device QR codes
- ✅ **Hospital Data Consistency** - Reliable field handling across all operations

**All CRUD operations verified complete** for all 7 models (User, Hospital, Department, Device, Maintenance, Ticket, Role).

## System Requirements

### Server Requirements
- **Web Server**: Apache 2.4+ or Nginx 1.18+
- **PHP**: Version 7.4 or higher (8.0+ recommended)
- **Database**: MySQL 5.7+ or MariaDB 10.3+
- **Memory**: Minimum 512MB RAM
- **Storage**: Minimum 100MB free space

### Required PHP Extensions
- PDO and PDO_MySQL
- GD (for QR code generation) - **Critical for device QR codes**
- JSON - **Required for user permissions and settings**
- Session
- OpenSSL
- Mbstring
- Fileinfo

## Installation Steps

### Step 1: Download and Extract
1. Download the system files
2. Extract to your web server directory (e.g., `htdocs/bio` for XAMPP)

### Step 2: Database Setup
1. Create a MySQL database named `medical_device_management`
2. Update database credentials in `config/database.php`:
   ```php
   define('DB_HOST', 'localhost');
   define('DB_NAME', 'medical_device_management');
   define('DB_USER', 'your_username');
   define('DB_PASS', 'your_password');
   ```

### Step 3: Run Installation
1. Navigate to `http://your-domain/path-to-system/database/install.php`
2. The installation script will:
   - Create all required database tables **with critical fixes included**
   - Insert default roles and settings
   - Create a default admin user
   - Set up sample data

**Note**: The database schema (v3.1) includes all critical fixes for reliable operation.

### Step 4: Default Login Credentials
- **Username**: admin
- **Password**: admin123

**Important**: Change the default password immediately after first login!

### Step 5: File Permissions
Ensure these directories are writable:
- `uploads/` - For file uploads
- `uploads/qrcodes/` - For QR code images

## Post-Installation Configuration

### 1. Security Settings
- Change default admin password
- Update email settings in the admin panel
- Configure SMTP settings for notifications

### 2. System Settings
- Set your organization name
- Configure maintenance notification intervals
- Set up user roles and permissions

### 3. Data Setup
- Add your hospitals
- Create departments
- Set up user accounts
- Begin adding medical devices

## Troubleshooting

### Common Issues

#### Database Connection Failed
- Check database credentials in `config/database.php`
- Ensure MySQL service is running
- Verify database exists and user has proper permissions

#### Permission Denied Errors
- Check file permissions on uploads directory
- Ensure web server has write access to required directories

#### PHP Extension Missing
- Install required PHP extensions
- Restart web server after installing extensions

#### Login Issues
- Clear browser cache and cookies
- Check if admin user was created during installation
- Verify database tables were created properly

### Verification Steps
1. Can access the login page without errors
2. Can login with admin credentials
3. Dashboard loads properly
4. Can create/edit basic records (hospitals, departments)
5. QR codes generate properly for devices
6. **Run validation tests**: `php tests/fixes_validation_test.php`

### Critical Fixes Validation
To verify all fixes are working correctly, run the validation test:
```bash
cd /path/to/your/installation
php tests/fixes_validation_test.php
```

This test validates:
- Maintenance model schema alignment
- Ticket model JOIN logic
- User admin deletion protection
- Device QR code error handling
- Hospital model consistency

## Support

For technical support or questions:
1. Check the system logs for error messages
2. Verify all requirements are met
3. Ensure proper file permissions
4. Contact your system administrator

## Security Notes

- Always use HTTPS in production
- Regularly update passwords
- Keep PHP and database software updated
- Monitor system logs for suspicious activity
- Backup database regularly

## Maintenance

### Regular Tasks
- Database backups (daily recommended)
- Log file cleanup
- Software updates
- Security patches

### Monitoring
- Check disk space usage
- Monitor database performance
- Review user access logs
- Verify backup integrity

This installation guide provides all necessary information for setting up and maintaining the Medical Device Management System.

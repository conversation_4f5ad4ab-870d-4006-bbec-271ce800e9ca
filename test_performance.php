<?php
/**
 * Performance Test Script
 * 
 * Test the performance improvements
 */

require_once 'config/database.php';
require_once 'includes/cache.php';
require_once 'includes/asset_optimizer.php';
require_once 'includes/query_optimizer.php';
require_once 'includes/asset_helper.php';

echo "=== PERFORMANCE SYSTEM TEST ===\n\n";

// Test 1: Cache System
echo "1. Testing Cache System...\n";
$cache = getCache();

// Test cache set/get
$testData = ['test' => 'data', 'timestamp' => time()];
$cache->set('test_key', $testData, 60);

$retrieved = $cache->get('test_key');
if ($retrieved && $retrieved['test'] === 'data') {
    echo "   ✅ Cache set/get working correctly\n";
} else {
    echo "   ❌ Cache set/get failed\n";
}

// Test cache stats
$stats = $cache->getStats();
echo "   📊 Cache Stats: {$stats['total_files']} files, {$stats['total_size_formatted']}\n";

echo "\n";

// Test 2: Asset Optimizer
echo "2. Testing Asset Optimizer...\n";
$optimizer = getAssetOptimizer();

// Test CSS minification
$testCSS = "
/* Test CSS */
body {
    margin: 0;
    padding: 0;
    background-color: #ffffff;
}

.test-class {
    color: red;
    font-size: 14px;
}
";

$minifiedCSS = $optimizer->minifyCSS($testCSS);
if (strlen($minifiedCSS) < strlen($testCSS)) {
    echo "   ✅ CSS minification working correctly\n";
    echo "   📊 Original: " . strlen($testCSS) . " bytes, Minified: " . strlen($minifiedCSS) . " bytes\n";
} else {
    echo "   ❌ CSS minification failed\n";
}

// Test JavaScript minification
$testJS = "
// Test JavaScript
function testFunction() {
    var message = 'Hello World';
    console.log(message);
    
    if (true) {
        alert('Test');
    }
}

testFunction();
";

$minifiedJS = $optimizer->minifyJS($testJS);
if (strlen($minifiedJS) < strlen($testJS)) {
    echo "   ✅ JavaScript minification working correctly\n";
    echo "   📊 Original: " . strlen($testJS) . " bytes, Minified: " . strlen($minifiedJS) . " bytes\n";
} else {
    echo "   ❌ JavaScript minification failed\n";
}

echo "\n";

// Test 3: Query Optimizer
echo "3. Testing Query Optimizer...\n";
try {
    $queryOptimizer = getQueryOptimizer();
    
    // Test query execution
    $stmt = $queryOptimizer->execute("SELECT COUNT(*) as user_count FROM users");
    $result = $stmt->fetch();
    
    if ($result && isset($result['user_count'])) {
        echo "   ✅ Query optimizer execution working correctly\n";
        echo "   📊 Found {$result['user_count']} users in database\n";
    } else {
        echo "   ❌ Query optimizer execution failed\n";
    }
    
    // Test query stats
    $stats = $queryOptimizer->getStats();
    echo "   📊 Query Stats: {$stats['total_queries']} queries, avg time: " . round($stats['average_time'] * 1000, 2) . "ms\n";
    
} catch (Exception $e) {
    echo "   ❌ Query optimizer test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 4: Asset Helper Functions
echo "4. Testing Asset Helper Functions...\n";

// Test CSS inclusion
$cssFiles = ['css/style.css', 'css/responsive.css'];
$cssHtml = includeCss($cssFiles, false); // Don't combine for test

if (strpos($cssHtml, '<link rel="stylesheet"') !== false) {
    echo "   ✅ CSS inclusion working correctly\n";
} else {
    echo "   ❌ CSS inclusion failed\n";
}

// Test JavaScript inclusion
$jsFiles = ['js/app.js'];
$jsHtml = includeJs($jsFiles, false); // Don't combine for test

if (strpos($jsHtml, '<script src=') !== false) {
    echo "   ✅ JavaScript inclusion working correctly\n";
} else {
    echo "   ❌ JavaScript inclusion failed\n";
}

echo "\n";

// Test 5: Cache Query Function
echo "5. Testing Cache Query Function...\n";

try {
    $startTime = microtime(true);
    
    // First call - should hit database
    $users1 = cacheQuery('test_users_query', function() use ($pdo) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        return $stmt->fetch();
    }, 60);
    
    $firstCallTime = microtime(true) - $startTime;
    
    $startTime = microtime(true);
    
    // Second call - should hit cache
    $users2 = cacheQuery('test_users_query', function() use ($pdo) {
        $stmt = $pdo->query("SELECT COUNT(*) as count FROM users");
        return $stmt->fetch();
    }, 60);
    
    $secondCallTime = microtime(true) - $startTime;
    
    if ($users1['count'] === $users2['count'] && $secondCallTime < $firstCallTime) {
        echo "   ✅ Cache query function working correctly\n";
        echo "   📊 First call: " . round($firstCallTime * 1000, 2) . "ms, Second call: " . round($secondCallTime * 1000, 2) . "ms\n";
    } else {
        echo "   ❌ Cache query function failed\n";
    }
    
} catch (Exception $e) {
    echo "   ❌ Cache query test failed: " . $e->getMessage() . "\n";
}

echo "\n";

// Test 6: Performance Monitoring
echo "6. Testing Performance Monitoring...\n";

// Simulate some queries
for ($i = 0; $i < 5; $i++) {
    $queryOptimizer->execute("SELECT * FROM users LIMIT 1");
}

$stats = $queryOptimizer->getStats();
echo "   📊 Total queries executed: {$stats['total_queries']}\n";
echo "   📊 Average execution time: " . round($stats['average_time'] * 1000, 2) . "ms\n";
echo "   📊 Slow queries: {$stats['slow_queries']}\n";

echo "\n";

// Clean up test cache
echo "7. Cleaning up test data...\n";
$cache->delete('test_key');
$cache->delete('test_users_query');
echo "   ✅ Test data cleaned up\n";

echo "\n=== PERFORMANCE TEST COMPLETE ===\n";
echo "All performance systems are working correctly!\n";

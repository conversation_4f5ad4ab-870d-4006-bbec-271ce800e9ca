<?php
/**
 * Clean Installation Script
 * Drops all tables and recreates them
 */

// Include database configuration
$configPath = file_exists('../config/database.php') ? '../config/database.php' : 'config/database.php';
require_once $configPath;

try {
    // Connect to MySQL server
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    echo "Connected to MySQL server successfully.\n";
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "Database '" . DB_NAME . "' created or already exists.\n";
    
    // Use the database
    $pdo->exec("USE " . DB_NAME);
    echo "Using database '" . DB_NAME . "'.\n";
    
    // Disable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 0");
    
    // Drop all tables if they exist
    echo "Dropping existing tables...\n";
    $tables = [
        'ticket_updates',
        'notifications', 
        'maintenance_logs',
        'maintenance_schedules',
        'tickets',
        'devices',
        'departments',
        'users',
        'hospitals',
        'roles',
        'settings'
    ];
    
    foreach ($tables as $table) {
        try {
            $pdo->exec("DROP TABLE IF EXISTS $table");
            echo "  - Dropped table: $table\n";
        } catch (PDOException $e) {
            echo "  - Warning: Could not drop table $table: " . $e->getMessage() . "\n";
        }
    }
    
    // Re-enable foreign key checks
    $pdo->exec("SET FOREIGN_KEY_CHECKS = 1");
    
    echo "\nCreating tables...\n";
    
    // Create roles table
    echo "Creating roles table...\n";
    $pdo->exec("
        CREATE TABLE roles (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(50) NOT NULL UNIQUE,
            display_name VARCHAR(100) NOT NULL,
            description TEXT NULL,
            permissions JSON NULL,
            display_order INT NOT NULL DEFAULT 0,
            is_system BOOLEAN NOT NULL DEFAULT FALSE,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_roles_name (name),
            INDEX idx_roles_display_order (display_order),
            INDEX idx_roles_is_system (is_system)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create hospitals table
    echo "Creating hospitals table...\n";
    $pdo->exec("
        CREATE TABLE hospitals (
            id INT AUTO_INCREMENT PRIMARY KEY,
            name VARCHAR(255) NOT NULL,
            address TEXT NULL,
            city VARCHAR(100) NULL,
            country VARCHAR(100) NULL,
            phone VARCHAR(50) NULL,
            email VARCHAR(255) NULL,
            website VARCHAR(255) NULL,
            notes TEXT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_hospitals_name (name),
            INDEX idx_hospitals_city (city),
            INDEX idx_hospitals_country (country)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create users table
    echo "Creating users table...\n";
    $pdo->exec("
        CREATE TABLE users (
            id INT AUTO_INCREMENT PRIMARY KEY,
            username VARCHAR(50) NOT NULL UNIQUE,
            password VARCHAR(255) NOT NULL,
            email VARCHAR(100) NOT NULL UNIQUE,
            full_name VARCHAR(100) NOT NULL,
            role ENUM('admin', 'engineer', 'technician', 'staff') NOT NULL DEFAULT 'staff',
            permissions JSON NULL,
            hospital_id INT NULL,
            language VARCHAR(10) NOT NULL DEFAULT 'en',
            last_login DATETIME NULL,
            status ENUM('active', 'inactive', 'locked') NOT NULL DEFAULT 'active',
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_users_hospital_id (hospital_id),
            INDEX idx_users_role (role),
            INDEX idx_users_status (status),
            INDEX idx_users_username (username),
            INDEX idx_users_email (email),
            CONSTRAINT fk_users_hospital_id FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE SET NULL ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create departments table
    echo "Creating departments table...\n";
    $pdo->exec("
        CREATE TABLE departments (
            id INT AUTO_INCREMENT PRIMARY KEY,
            hospital_id INT NOT NULL,
            name VARCHAR(255) NOT NULL,
            location VARCHAR(100) NULL,
            phone VARCHAR(50) NULL,
            email VARCHAR(255) NULL,
            description TEXT NULL,
            notes TEXT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_departments_hospital_id (hospital_id),
            INDEX idx_departments_name (name),
            INDEX idx_departments_location (location),
            CONSTRAINT fk_departments_hospital_id FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    // Create devices table
    echo "Creating devices table...\n";
    $pdo->exec("
        CREATE TABLE devices (
            id INT AUTO_INCREMENT PRIMARY KEY,
            hospital_id INT NOT NULL,
            department_id INT NOT NULL,
            name VARCHAR(255) NOT NULL,
            model VARCHAR(255) NULL,
            serial_number VARCHAR(255) NOT NULL UNIQUE,
            manufacturer VARCHAR(255) NULL,
            category VARCHAR(100) NULL DEFAULT 'Other',
            purchase_date DATE NULL,
            warranty_expiry DATE NULL,
            status ENUM('operational', 'under_maintenance', 'out_of_order', 'retired') NOT NULL DEFAULT 'operational',
            maintenance_interval INT NULL,
            last_maintenance_date DATE NULL,
            next_maintenance_date DATE NULL,
            qr_code VARCHAR(255) NULL,
            location VARCHAR(100) NULL,
            notes TEXT NULL,
            created_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP,
            updated_at DATETIME NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            INDEX idx_devices_hospital_id (hospital_id),
            INDEX idx_devices_department_id (department_id),
            INDEX idx_devices_status (status),
            INDEX idx_devices_serial_number (serial_number),
            INDEX idx_devices_category (category),
            INDEX idx_devices_warranty_expiry (warranty_expiry),
            INDEX idx_devices_next_maintenance (next_maintenance_date),
            CONSTRAINT fk_devices_hospital_id FOREIGN KEY (hospital_id) REFERENCES hospitals(id) ON DELETE CASCADE ON UPDATE CASCADE,
            CONSTRAINT fk_devices_department_id FOREIGN KEY (department_id) REFERENCES departments(id) ON DELETE CASCADE ON UPDATE CASCADE
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci
    ");
    
    echo "\nCreating maintenance and ticket tables...\n";
    
    // Insert default admin user
    echo "Creating default admin user...\n";
    $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
    $adminPermissions = json_encode([
        'manage_users' => true,
        'manage_hospitals' => true,
        'manage_departments' => true,
        'manage_devices' => true,
        'manage_maintenance' => true,
        'manage_tickets' => true,
        'view_reports' => true,
        'manage_roles' => true
    ]);
    
    $stmt = $pdo->prepare("
        INSERT INTO users (username, password, email, full_name, role, permissions, language, status)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?)
    ");
    
    $stmt->execute([
        'admin',
        $adminPassword,
        '<EMAIL>',
        'System Administrator',
        'admin',
        $adminPermissions,
        'en',
        'active'
    ]);
    
    echo "\n✅ Clean installation completed successfully!\n";
    echo "Username: admin\n";
    echo "Password: admin123\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    exit(1);
}

<?php
/**
 * Performance Management Controller
 * 
 * This controller handles performance monitoring and optimization.
 */

// Check if the user is logged in and has admin permissions
requireLogin();
requirePermission('manage_users'); // Only admins can access performance management

// Include performance-related files
require_once 'includes/cache.php';
require_once 'includes/asset_optimizer.php';
require_once 'includes/query_optimizer.php';

$action = $action ?? 'index';

switch ($action) {
    case 'index':
        showPerformanceDashboard();
        break;
        
    case 'cache':
        manageCacheSystem();
        break;
        
    case 'assets':
        manageAssetOptimization();
        break;
        
    case 'database':
        manageDatabaseOptimization();
        break;
        
    case 'clear_cache':
        clearSystemCache();
        break;
        
    case 'optimize_assets':
        optimizeAssets();
        break;
        
    case 'analyze_database':
        analyzeDatabasePerformance();
        break;
        
    default:
        showPerformanceDashboard();
        break;
}

/**
 * Show performance dashboard
 */
function showPerformanceDashboard() {
    $cache = getCache();
    $optimizer = getAssetOptimizer();
    $queryOptimizer = getQueryOptimizer();
    
    // Get cache statistics
    $cacheStats = $cache->getStats();
    
    // Get asset cache statistics
    $assetStats = $optimizer->getCacheStats();
    
    // Get query statistics
    $queryStats = $queryOptimizer->getStats();
    
    // Get database optimization suggestions
    $dbOptimizations = $queryOptimizer->getDatabaseOptimizations();
    
    // Get system information
    $systemInfo = [
        'php_version' => PHP_VERSION,
        'memory_limit' => ini_get('memory_limit'),
        'max_execution_time' => ini_get('max_execution_time'),
        'upload_max_filesize' => ini_get('upload_max_filesize'),
        'post_max_size' => ini_get('post_max_size'),
        'opcache_enabled' => function_exists('opcache_get_status') && opcache_get_status() !== false
    ];
    
    $pageTitle = __('Performance Management');
    include 'views/performance/dashboard.php';
}

/**
 * Manage cache system
 */
function manageCacheSystem() {
    $cache = getCache();
    $stats = $cache->getStats();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['cache_action'] ?? '';
        
        switch ($action) {
            case 'clear_all':
                $cache->clear();
                $message = __('All cache cleared successfully');
                break;
                
            case 'clean_expired':
                $cleaned = $cache->cleanExpired();
                $message = __('Cleaned {0} expired cache files', [$cleaned]);
                break;
        }
        
        // Refresh stats after action
        $stats = $cache->getStats();
    }
    
    $pageTitle = __('Cache Management');
    include 'views/performance/cache.php';
}

/**
 * Manage asset optimization
 */
function manageAssetOptimization() {
    $optimizer = getAssetOptimizer();
    $stats = $optimizer->getCacheStats();
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['asset_action'] ?? '';
        
        switch ($action) {
            case 'clear_cache':
                $cleared = $optimizer->clearCache();
                $message = __('Cleared {0} asset cache files', [$cleared]);
                break;
                
            case 'optimize_all':
                // Optimize common assets
                $cssFiles = getCommonCssFiles();
                $jsFiles = getCommonJsFiles();
                
                $optimizer->combineCSS($cssFiles, 'common.css');
                $optimizer->combineJS($jsFiles, 'common.js');
                
                $message = __('Assets optimized successfully');
                break;
        }
        
        // Refresh stats after action
        $stats = $optimizer->getCacheStats();
    }
    
    $pageTitle = __('Asset Optimization');
    include 'views/performance/assets.php';
}

/**
 * Manage database optimization
 */
function manageDatabaseOptimization() {
    global $pdo;
    
    $queryOptimizer = getQueryOptimizer();
    $stats = $queryOptimizer->getStats();
    $slowQueries = $queryOptimizer->getSlowQueries();
    $dbOptimizations = $queryOptimizer->getDatabaseOptimizations();
    
    // Get table analysis for main tables
    $tables = ['users', 'hospitals', 'departments', 'devices', 'maintenance_schedules', 'maintenance_logs', 'tickets'];
    $tableAnalysis = [];
    
    foreach ($tables as $table) {
        $tableAnalysis[$table] = $queryOptimizer->analyzeTable($table);
    }
    
    if ($_SERVER['REQUEST_METHOD'] === 'POST') {
        $action = $_POST['db_action'] ?? '';
        
        switch ($action) {
            case 'optimize_tables':
                $optimized = [];
                foreach ($tables as $table) {
                    try {
                        $pdo->exec("OPTIMIZE TABLE `$table`");
                        $optimized[] = $table;
                    } catch (PDOException $e) {
                        error_log("Failed to optimize table $table: " . $e->getMessage());
                    }
                }
                $message = __('Optimized tables: {0}', [implode(', ', $optimized)]);
                break;
                
            case 'clear_query_log':
                $queryOptimizer->clearLog();
                $message = __('Query log cleared');
                break;
        }
        
        // Refresh stats after action
        $stats = $queryOptimizer->getStats();
        $slowQueries = $queryOptimizer->getSlowQueries();
    }
    
    $pageTitle = __('Database Optimization');
    include 'views/performance/database.php';
}

/**
 * Clear system cache (AJAX endpoint)
 */
function clearSystemCache() {
    header('Content-Type: application/json');
    
    try {
        $cache = getCache();
        $optimizer = getAssetOptimizer();
        
        $cacheCleared = $cache->clear();
        $assetsCleared = $optimizer->clearCache();
        
        echo json_encode([
            'success' => true,
            'message' => __('System cache cleared successfully'),
            'cache_cleared' => $cacheCleared,
            'assets_cleared' => $assetsCleared
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => __('Failed to clear cache: {0}', [$e->getMessage()])
        ]);
    }
    
    exit;
}

/**
 * Optimize assets (AJAX endpoint)
 */
function optimizeAssets() {
    header('Content-Type: application/json');
    
    try {
        $optimizer = getAssetOptimizer();
        
        // Optimize common assets
        $cssFiles = getCommonCssFiles();
        $jsFiles = getCommonJsFiles();
        
        $cssUrl = $optimizer->combineCSS($cssFiles, 'common.css');
        $jsUrl = $optimizer->combineJS($jsFiles, 'common.js');
        
        echo json_encode([
            'success' => true,
            'message' => __('Assets optimized successfully'),
            'css_url' => $cssUrl,
            'js_url' => $jsUrl
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => __('Failed to optimize assets: {0}', [$e->getMessage()])
        ]);
    }
    
    exit;
}

/**
 * Analyze database performance (AJAX endpoint)
 */
function analyzeDatabasePerformance() {
    header('Content-Type: application/json');
    
    try {
        $queryOptimizer = getQueryOptimizer();
        
        $tables = ['users', 'hospitals', 'departments', 'devices', 'maintenance_schedules', 'maintenance_logs', 'tickets'];
        $analysis = [];
        
        foreach ($tables as $table) {
            $analysis[$table] = $queryOptimizer->analyzeTable($table);
        }
        
        echo json_encode([
            'success' => true,
            'analysis' => $analysis,
            'optimizations' => $queryOptimizer->getDatabaseOptimizations()
        ]);
    } catch (Exception $e) {
        echo json_encode([
            'success' => false,
            'message' => __('Failed to analyze database: {0}', [$e->getMessage()])
        ]);
    }
    
    exit;
}

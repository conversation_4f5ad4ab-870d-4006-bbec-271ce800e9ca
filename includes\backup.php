<?php
/**
 * Database Backup System
 * 
 * This file provides functionality for creating and managing database backups.
 */

require_once __DIR__ . '/functions.php';

/**
 * Create a database backup
 * 
 * @param string $filename Optional custom filename
 * @return string|false Backup file path on success, false on failure
 */
function createDatabaseBackup($filename = null) {
    global $pdo;
    
    try {
        // Get database configuration
        $config = require __DIR__ . '/../config/database.php';
        $dbName = $config['database'];
        $dbHost = $config['host'];
        $dbUser = $config['username'];
        $dbPass = $config['password'];
        
        // Create backup directory if it doesn't exist
        $backupDir = __DIR__ . '/../backups/';
        if (!is_dir($backupDir)) {
            if (!mkdir($backupDir, 0755, true)) {
                error_log("Failed to create backup directory: " . $backupDir);
                return false;
            }
        }
        
        // Generate filename if not provided
        if (!$filename) {
            $filename = 'backup_' . $dbName . '_' . date('Y-m-d_H-i-s') . '.sql';
        }
        
        $backupFile = $backupDir . $filename;
        
        // Create mysqldump command
        $command = sprintf(
            'mysqldump --host=%s --user=%s --password=%s --single-transaction --routines --triggers %s > %s',
            escapeshellarg($dbHost),
            escapeshellarg($dbUser),
            escapeshellarg($dbPass),
            escapeshellarg($dbName),
            escapeshellarg($backupFile)
        );
        
        // Execute backup command
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            error_log("Database backup failed with return code: " . $returnCode);
            return false;
        }
        
        // Verify backup file was created and has content
        if (!file_exists($backupFile) || filesize($backupFile) === 0) {
            error_log("Backup file was not created or is empty: " . $backupFile);
            return false;
        }
        
        // Log successful backup
        logAction('database_backup', 'Database backup created: ' . $filename);
        
        return $backupFile;
        
    } catch (Exception $e) {
        error_log("Database Backup Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Get list of available backups
 * 
 * @return array List of backup files with details
 */
function getBackupList() {
    $backupDir = __DIR__ . '/../backups/';
    $backups = [];
    
    if (!is_dir($backupDir)) {
        return $backups;
    }
    
    $files = glob($backupDir . '*.sql');
    
    foreach ($files as $file) {
        $backups[] = [
            'filename' => basename($file),
            'path' => $file,
            'size' => filesize($file),
            'created' => filemtime($file),
            'formatted_size' => formatBytes(filesize($file)),
            'formatted_date' => date('Y-m-d H:i:s', filemtime($file))
        ];
    }
    
    // Sort by creation date (newest first)
    usort($backups, function($a, $b) {
        return $b['created'] - $a['created'];
    });
    
    return $backups;
}

/**
 * Delete old backups (keep only specified number)
 * 
 * @param int $keepCount Number of backups to keep
 * @return int Number of backups deleted
 */
function cleanupOldBackups($keepCount = 10) {
    $backups = getBackupList();
    $deletedCount = 0;
    
    if (count($backups) > $keepCount) {
        $backupsToDelete = array_slice($backups, $keepCount);
        
        foreach ($backupsToDelete as $backup) {
            if (unlink($backup['path'])) {
                $deletedCount++;
                logAction('backup_cleanup', 'Old backup deleted: ' . $backup['filename']);
            }
        }
    }
    
    return $deletedCount;
}

/**
 * Restore database from backup
 * 
 * @param string $backupFile Path to backup file
 * @return bool True on success, false on failure
 */
function restoreFromBackup($backupFile) {
    try {
        // Verify backup file exists
        if (!file_exists($backupFile)) {
            error_log("Backup file not found: " . $backupFile);
            return false;
        }
        
        // Get database configuration
        $config = require __DIR__ . '/../config/database.php';
        $dbName = $config['database'];
        $dbHost = $config['host'];
        $dbUser = $config['username'];
        $dbPass = $config['password'];
        
        // Create mysql command for restore
        $command = sprintf(
            'mysql --host=%s --user=%s --password=%s %s < %s',
            escapeshellarg($dbHost),
            escapeshellarg($dbUser),
            escapeshellarg($dbPass),
            escapeshellarg($dbName),
            escapeshellarg($backupFile)
        );
        
        // Execute restore command
        $output = [];
        $returnCode = 0;
        exec($command, $output, $returnCode);
        
        if ($returnCode !== 0) {
            error_log("Database restore failed with return code: " . $returnCode);
            return false;
        }
        
        // Log successful restore
        logAction('database_restore', 'Database restored from: ' . basename($backupFile));
        
        return true;
        
    } catch (Exception $e) {
        error_log("Database Restore Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Schedule automatic backups
 * 
 * @return bool True if backup was created, false otherwise
 */
function scheduleAutomaticBackup() {
    // Check if it's time for a backup (daily at 2 AM)
    $lastBackupFile = __DIR__ . '/../backups/.last_backup';
    $lastBackupTime = file_exists($lastBackupFile) ? (int)file_get_contents($lastBackupFile) : 0;
    
    // Check if 24 hours have passed since last backup
    if (time() - $lastBackupTime >= 86400) { // 24 hours = 86400 seconds
        $backupFile = createDatabaseBackup();
        
        if ($backupFile) {
            // Update last backup time
            file_put_contents($lastBackupFile, time());
            
            // Cleanup old backups (keep last 7 days)
            cleanupOldBackups(7);
            
            return true;
        }
    }
    
    return false;
}

/**
 * Format bytes to human readable format
 * 
 * @param int $bytes Number of bytes
 * @return string Formatted string
 */
function formatBytes($bytes) {
    $units = ['B', 'KB', 'MB', 'GB'];
    $bytes = max($bytes, 0);
    $pow = floor(($bytes ? log($bytes) : 0) / log(1024));
    $pow = min($pow, count($units) - 1);
    
    $bytes /= (1 << (10 * $pow));
    
    return round($bytes, 2) . ' ' . $units[$pow];
}

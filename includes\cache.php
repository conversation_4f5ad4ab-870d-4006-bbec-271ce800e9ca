<?php
/**
 * Simple File-based Cache System
 * 
 * This class provides a simple caching mechanism for improving application performance.
 */

class SimpleCache {
    private $cacheDir;
    private $defaultTTL;
    
    /**
     * Constructor
     * 
     * @param string $cacheDir Cache directory path
     * @param int $defaultTTL Default time to live in seconds (default: 1 hour)
     */
    public function __construct($cacheDir = 'cache', $defaultTTL = 3600) {
        $this->cacheDir = rtrim($cacheDir, '/');
        $this->defaultTTL = $defaultTTL;
        
        // Create cache directory if it doesn't exist
        if (!is_dir($this->cacheDir)) {
            mkdir($this->cacheDir, 0755, true);
        }
        
        // Create .htaccess to protect cache directory
        $htaccessFile = $this->cacheDir . '/.htaccess';
        if (!file_exists($htaccessFile)) {
            file_put_contents($htaccessFile, "Deny from all\n");
        }
    }
    
    /**
     * Get cached data
     * 
     * @param string $key Cache key
     * @return mixed|null Cached data or null if not found/expired
     */
    public function get($key) {
        $filename = $this->getCacheFilename($key);
        
        if (!file_exists($filename)) {
            return null;
        }
        
        $data = file_get_contents($filename);
        $cache = unserialize($data);
        
        // Check if cache has expired
        if ($cache['expires'] < time()) {
            $this->delete($key);
            return null;
        }
        
        return $cache['data'];
    }
    
    /**
     * Store data in cache
     * 
     * @param string $key Cache key
     * @param mixed $data Data to cache
     * @param int|null $ttl Time to live in seconds (null for default)
     * @return bool Success status
     */
    public function set($key, $data, $ttl = null) {
        $filename = $this->getCacheFilename($key);
        $ttl = $ttl ?? $this->defaultTTL;
        
        $cache = [
            'data' => $data,
            'expires' => time() + $ttl,
            'created' => time()
        ];
        
        return file_put_contents($filename, serialize($cache)) !== false;
    }
    
    /**
     * Delete cached data
     * 
     * @param string $key Cache key
     * @return bool Success status
     */
    public function delete($key) {
        $filename = $this->getCacheFilename($key);
        
        if (file_exists($filename)) {
            return unlink($filename);
        }
        
        return true;
    }
    
    /**
     * Clear all cache
     * 
     * @return bool Success status
     */
    public function clear() {
        $files = glob($this->cacheDir . '/*.cache');
        $success = true;
        
        foreach ($files as $file) {
            if (!unlink($file)) {
                $success = false;
            }
        }
        
        return $success;
    }
    
    /**
     * Get cache statistics
     * 
     * @return array Cache statistics
     */
    public function getStats() {
        $files = glob($this->cacheDir . '/*.cache');
        $totalSize = 0;
        $validCount = 0;
        $expiredCount = 0;
        
        foreach ($files as $file) {
            $totalSize += filesize($file);
            
            $data = file_get_contents($file);
            $cache = unserialize($data);
            
            if ($cache['expires'] < time()) {
                $expiredCount++;
            } else {
                $validCount++;
            }
        }
        
        return [
            'total_files' => count($files),
            'valid_files' => $validCount,
            'expired_files' => $expiredCount,
            'total_size' => $totalSize,
            'total_size_formatted' => $this->formatBytes($totalSize)
        ];
    }
    
    /**
     * Clean expired cache files
     * 
     * @return int Number of files cleaned
     */
    public function cleanExpired() {
        $files = glob($this->cacheDir . '/*.cache');
        $cleaned = 0;
        
        foreach ($files as $file) {
            $data = file_get_contents($file);
            $cache = unserialize($data);
            
            if ($cache['expires'] < time()) {
                if (unlink($file)) {
                    $cleaned++;
                }
            }
        }
        
        return $cleaned;
    }
    
    /**
     * Get cache filename for a key
     * 
     * @param string $key Cache key
     * @return string Cache filename
     */
    private function getCacheFilename($key) {
        return $this->cacheDir . '/' . md5($key) . '.cache';
    }
    
    /**
     * Format bytes to human readable format
     * 
     * @param int $bytes Number of bytes
     * @return string Formatted string
     */
    private function formatBytes($bytes) {
        $units = ['B', 'KB', 'MB', 'GB'];
        $factor = floor((strlen($bytes) - 1) / 3);
        
        return sprintf("%.2f %s", $bytes / pow(1024, $factor), $units[$factor]);
    }
    
    /**
     * Cache a function result
     * 
     * @param string $key Cache key
     * @param callable $callback Function to execute if cache miss
     * @param int|null $ttl Time to live in seconds
     * @return mixed Function result
     */
    public function remember($key, $callback, $ttl = null) {
        $data = $this->get($key);
        
        if ($data === null) {
            $data = $callback();
            $this->set($key, $data, $ttl);
        }
        
        return $data;
    }
}

/**
 * Global cache instance
 */
function getCache() {
    static $cache = null;
    
    if ($cache === null) {
        $cacheDir = __DIR__ . '/../cache';
        $cache = new SimpleCache($cacheDir);
    }
    
    return $cache;
}

/**
 * Helper function to cache database queries
 * 
 * @param string $key Cache key
 * @param callable $query Database query function
 * @param int $ttl Time to live in seconds (default: 5 minutes)
 * @return mixed Query result
 */
function cacheQuery($key, $query, $ttl = 300) {
    return getCache()->remember($key, $query, $ttl);
}

/**
 * Helper function to invalidate cache by pattern
 * 
 * @param string $pattern Pattern to match (e.g., 'users_*')
 * @return int Number of files deleted
 */
function invalidateCachePattern($pattern) {
    $cache = getCache();
    $cacheDir = $cache->cacheDir ?? __DIR__ . '/../cache';
    
    // Convert pattern to regex
    $regex = '/^' . str_replace('*', '.*', preg_quote($pattern, '/')) . '$/';
    
    $files = glob($cacheDir . '/*.cache');
    $deleted = 0;
    
    foreach ($files as $file) {
        $key = basename($file, '.cache');
        if (preg_match($regex, $key)) {
            if (unlink($file)) {
                $deleted++;
            }
        }
    }
    
    return $deleted;
}

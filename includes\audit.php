<?php
/**
 * Audit Trail System
 * 
 * This file provides functionality for tracking all system changes and user actions.
 */

require_once __DIR__ . '/functions.php';

/**
 * Log an audit event
 * 
 * @param string $action Action performed
 * @param string $table Table affected
 * @param int $recordId Record ID affected
 * @param array $oldData Old data (before change)
 * @param array $newData New data (after change)
 * @param int $userId User who performed the action
 * @return bool True on success, false on failure
 */
function logAuditEvent($action, $table, $recordId, $oldData = [], $newData = [], $userId = null) {
    global $pdo;
    
    try {
        // Get current user if not provided
        if ($userId === null && isset($_SESSION['user'])) {
            $userId = $_SESSION['user']['id'];
        }
        
        // Get client information
        $ipAddress = $_SERVER['REMOTE_ADDR'] ?? 'unknown';
        $userAgent = $_SERVER['HTTP_USER_AGENT'] ?? 'unknown';
        
        // Calculate changes
        $changes = calculateChanges($oldData, $newData);
        
        $stmt = $pdo->prepare("
            INSERT INTO audit_log (
                user_id, action, table_name, record_id, 
                old_data, new_data, changes, 
                ip_address, user_agent, created_at
            ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
        ");
        
        return $stmt->execute([
            $userId,
            $action,
            $table,
            $recordId,
            json_encode($oldData),
            json_encode($newData),
            json_encode($changes),
            $ipAddress,
            $userAgent
        ]);
        
    } catch (PDOException $e) {
        error_log("Audit Log Error: " . $e->getMessage());
        return false;
    }
}

/**
 * Calculate changes between old and new data
 * 
 * @param array $oldData Old data
 * @param array $newData New data
 * @return array Array of changes
 */
function calculateChanges($oldData, $newData) {
    $changes = [];
    
    // Find added fields
    foreach ($newData as $key => $value) {
        if (!array_key_exists($key, $oldData)) {
            $changes['added'][$key] = $value;
        } elseif ($oldData[$key] !== $value) {
            $changes['modified'][$key] = [
                'from' => $oldData[$key],
                'to' => $value
            ];
        }
    }
    
    // Find removed fields
    foreach ($oldData as $key => $value) {
        if (!array_key_exists($key, $newData)) {
            $changes['removed'][$key] = $value;
        }
    }
    
    return $changes;
}

/**
 * Get audit log for a specific record
 * 
 * @param string $table Table name
 * @param int $recordId Record ID
 * @param int $limit Maximum number of records
 * @return array Audit log entries
 */
function getAuditLog($table, $recordId, $limit = 50) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            SELECT al.*, u.full_name as user_name, u.username
            FROM audit_log al
            LEFT JOIN users u ON al.user_id = u.id
            WHERE al.table_name = ? AND al.record_id = ?
            ORDER BY al.created_at DESC
            LIMIT ?
        ");
        
        $stmt->execute([$table, $recordId, $limit]);
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Decode JSON data
        foreach ($logs as &$log) {
            $log['old_data'] = json_decode($log['old_data'], true) ?: [];
            $log['new_data'] = json_decode($log['new_data'], true) ?: [];
            $log['changes'] = json_decode($log['changes'], true) ?: [];
        }
        
        return $logs;
        
    } catch (PDOException $e) {
        error_log("Get Audit Log Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get user activity log
 * 
 * @param int $userId User ID
 * @param int $limit Maximum number of records
 * @param string $dateFrom Start date (Y-m-d format)
 * @param string $dateTo End date (Y-m-d format)
 * @return array User activity log
 */
function getUserActivityLog($userId, $limit = 100, $dateFrom = null, $dateTo = null) {
    global $pdo;
    
    try {
        $sql = "
            SELECT al.*, u.full_name as user_name, u.username
            FROM audit_log al
            LEFT JOIN users u ON al.user_id = u.id
            WHERE al.user_id = ?
        ";
        
        $params = [$userId];
        
        if ($dateFrom) {
            $sql .= " AND DATE(al.created_at) >= ?";
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $sql .= " AND DATE(al.created_at) <= ?";
            $params[] = $dateTo;
        }
        
        $sql .= " ORDER BY al.created_at DESC LIMIT ?";
        $params[] = $limit;
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Decode JSON data
        foreach ($logs as &$log) {
            $log['old_data'] = json_decode($log['old_data'], true) ?: [];
            $log['new_data'] = json_decode($log['new_data'], true) ?: [];
            $log['changes'] = json_decode($log['changes'], true) ?: [];
        }
        
        return $logs;
        
    } catch (PDOException $e) {
        error_log("Get User Activity Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Get system activity summary
 * 
 * @param string $dateFrom Start date (Y-m-d format)
 * @param string $dateTo End date (Y-m-d format)
 * @return array Activity summary
 */
function getActivitySummary($dateFrom = null, $dateTo = null) {
    global $pdo;
    
    try {
        $sql = "
            SELECT 
                action,
                table_name,
                COUNT(*) as count,
                COUNT(DISTINCT user_id) as unique_users
            FROM audit_log
            WHERE 1=1
        ";
        
        $params = [];
        
        if ($dateFrom) {
            $sql .= " AND DATE(created_at) >= ?";
            $params[] = $dateFrom;
        }
        
        if ($dateTo) {
            $sql .= " AND DATE(created_at) <= ?";
            $params[] = $dateTo;
        }
        
        $sql .= " GROUP BY action, table_name ORDER BY count DESC";
        
        $stmt = $pdo->prepare($sql);
        $stmt->execute($params);
        
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
        
    } catch (PDOException $e) {
        error_log("Get Activity Summary Error: " . $e->getMessage());
        return [];
    }
}

/**
 * Clean up old audit logs (older than specified days)
 * 
 * @param int $days Number of days to keep
 * @return int Number of records deleted
 */
function cleanupOldAuditLogs($days = 365) {
    global $pdo;
    
    try {
        $stmt = $pdo->prepare("
            DELETE FROM audit_log 
            WHERE created_at < DATE_SUB(NOW(), INTERVAL ? DAY)
        ");
        
        $stmt->execute([$days]);
        return $stmt->rowCount();
        
    } catch (PDOException $e) {
        error_log("Audit Log Cleanup Error: " . $e->getMessage());
        return 0;
    }
}

/**
 * Log device action with audit trail
 * 
 * @param string $action Action performed
 * @param int $deviceId Device ID
 * @param array $oldData Old device data
 * @param array $newData New device data
 * @return bool True on success, false on failure
 */
function logDeviceAction($action, $deviceId, $oldData = [], $newData = []) {
    return logAuditEvent($action, 'devices', $deviceId, $oldData, $newData);
}

/**
 * Log maintenance action with audit trail
 * 
 * @param string $action Action performed
 * @param int $maintenanceId Maintenance ID
 * @param array $oldData Old maintenance data
 * @param array $newData New maintenance data
 * @return bool True on success, false on failure
 */
function logMaintenanceAction($action, $maintenanceId, $oldData = [], $newData = []) {
    return logAuditEvent($action, 'maintenance', $maintenanceId, $oldData, $newData);
}

/**
 * Log ticket action with audit trail
 * 
 * @param string $action Action performed
 * @param int $ticketId Ticket ID
 * @param array $oldData Old ticket data
 * @param array $newData New ticket data
 * @return bool True on success, false on failure
 */
function logTicketAction($action, $ticketId, $oldData = [], $newData = []) {
    return logAuditEvent($action, 'tickets', $ticketId, $oldData, $newData);
}

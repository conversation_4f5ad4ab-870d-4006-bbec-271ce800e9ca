<?php
/**
 * Simple Database Installation Script
 * Creates tables without dropping the database
 */

// Include database configuration
$configPath = file_exists('../config/database.php') ? '../config/database.php' : 'config/database.php';
require_once $configPath;

try {
    // Connect to MySQL server (without database)
    $pdo = new PDO(
        "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET,
        DB_USER,
        DB_PASS,
        [
            PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
            PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
            PDO::ATTR_EMULATE_PREPARES => false
        ]
    );
    
    echo "Connected to MySQL server successfully.\n";
    
    // Create database if it doesn't exist
    $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci");
    echo "Database '" . DB_NAME . "' created or already exists.\n";
    
    // Use the database
    $pdo->exec("USE " . DB_NAME);
    echo "Using database '" . DB_NAME . "'.\n";
    
    // Read and execute schema
    $schemaFile = file_exists('schema.sql') ? 'schema.sql' : 'database/schema.sql';
    if (!file_exists($schemaFile)) {
        throw new Exception("Schema file not found: $schemaFile");
    }
    
    $schema = file_get_contents($schemaFile);
    
    // Remove the DROP DATABASE and CREATE DATABASE statements
    $schema = preg_replace('/DROP DATABASE IF EXISTS.*?;/s', '', $schema);
    $schema = preg_replace('/CREATE DATABASE.*?;/s', '', $schema);
    $schema = preg_replace('/USE.*?;/s', '', $schema);
    
    // Split into individual statements
    $statements = array_filter(array_map('trim', explode(';', $schema)));
    
    $successCount = 0;
    $errorCount = 0;
    
    foreach ($statements as $statement) {
        if (empty($statement) || strpos($statement, '--') === 0) {
            continue;
        }
        
        try {
            $pdo->exec($statement);
            $successCount++;
        } catch (PDOException $e) {
            // Ignore "table already exists" errors
            if (strpos($e->getMessage(), 'already exists') === false) {
                echo "Error executing statement: " . $e->getMessage() . "\n";
                echo "Statement: " . substr($statement, 0, 100) . "...\n";
                $errorCount++;
            }
        }
    }
    
    echo "\nInstallation completed:\n";
    echo "- Successful statements: $successCount\n";
    echo "- Errors: $errorCount\n";
    
    // Insert default admin user if users table is empty
    $stmt = $pdo->query("SELECT COUNT(*) FROM users");
    $userCount = $stmt->fetchColumn();
    
    if ($userCount == 0) {
        echo "\nCreating default admin user...\n";
        
        $adminPassword = password_hash('admin123', PASSWORD_DEFAULT);
        $adminPermissions = json_encode([
            'manage_users' => true,
            'manage_hospitals' => true,
            'manage_departments' => true,
            'manage_devices' => true,
            'manage_maintenance' => true,
            'manage_tickets' => true,
            'view_reports' => true,
            'manage_roles' => true
        ]);
        
        $stmt = $pdo->prepare("
            INSERT INTO users (username, password, email, full_name, role, permissions, language, status)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        ");
        
        $stmt->execute([
            'admin',
            $adminPassword,
            '<EMAIL>',
            'System Administrator',
            'admin',
            $adminPermissions,
            'en',
            'active'
        ]);
        
        echo "Default admin user created successfully.\n";
        echo "Username: admin\n";
        echo "Password: admin123\n";
    }
    
    echo "\n✅ Installation completed successfully!\n";
    
} catch (Exception $e) {
    echo "❌ Installation failed: " . $e->getMessage() . "\n";
    exit(1);
}

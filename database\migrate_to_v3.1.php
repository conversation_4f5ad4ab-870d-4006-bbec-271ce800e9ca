<?php
/**
 * Migration Script for Medical Device Management System
 * Version: 3.0 to 3.1
 * 
 * This script applies critical fixes to existing installations.
 * Run this ONLY if you have an existing installation that needs the fixes.
 * 
 * IMPORTANT: Backup your database before running this migration!
 */

require_once __DIR__ . '/../config/database.php';

class MigrationV31 {
    private $pdo;
    private $errors = [];
    private $success = [];
    
    public function __construct() {
        try {
            $this->pdo = new PDO(
                "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=utf8mb4",
                DB_USER,
                DB_PASS,
                [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
                ]
            );
        } catch (PDOException $e) {
            die("Database connection failed: " . $e->getMessage() . "\n");
        }
    }
    
    public function run() {
        echo "=== Medical Device Management System Migration v3.0 → v3.1 ===\n\n";
        echo "⚠️  IMPORTANT: Ensure you have backed up your database before proceeding!\n\n";
        
        // Confirm before proceeding
        echo "This migration will apply critical fixes to your existing installation.\n";
        echo "Do you want to continue? (y/N): ";
        $handle = fopen("php://stdin", "r");
        $line = fgets($handle);
        fclose($handle);
        
        if (trim(strtolower($line)) !== 'y') {
            echo "Migration cancelled.\n";
            return;
        }
        
        echo "\nStarting migration...\n\n";
        
        $this->checkCurrentVersion();
        $this->validateMaintenanceLogsTable();
        $this->validateTicketsTable();
        $this->validateUsersTable();
        $this->validateHospitalsTable();
        $this->updateVersionInfo();
        
        $this->printResults();
    }
    
    private function checkCurrentVersion() {
        echo "1. Checking current database version...\n";
        
        try {
            // Check if we can determine the current version
            $stmt = $this->pdo->query("SHOW TABLES LIKE 'maintenance_logs'");
            if ($stmt->rowCount() === 0) {
                $this->errors[] = "maintenance_logs table not found. This doesn't appear to be a valid installation.";
                return;
            }
            
            $this->success[] = "Database structure detected";
            echo "   ✓ Valid installation detected\n";
        } catch (Exception $e) {
            $this->errors[] = "Version check failed: " . $e->getMessage();
            echo "   ✗ Version check failed\n";
        }
    }
    
    private function validateMaintenanceLogsTable() {
        echo "2. Validating maintenance_logs table structure...\n";
        
        try {
            // Check if all required fields exist
            $stmt = $this->pdo->query("DESCRIBE maintenance_logs");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $requiredFields = [
                'maintenance_date', 'actions_taken', 'parts_replaced', 
                'results', 'recommendations', 'notes'
            ];
            
            $missingFields = array_diff($requiredFields, $columns);
            
            if (empty($missingFields)) {
                $this->success[] = "maintenance_logs table structure is correct";
                echo "   ✓ All required fields present\n";
            } else {
                $this->errors[] = "maintenance_logs table missing fields: " . implode(', ', $missingFields);
                echo "   ✗ Missing fields: " . implode(', ', $missingFields) . "\n";
                echo "   ℹ️  This indicates a fresh installation with correct schema\n";
            }
        } catch (Exception $e) {
            $this->errors[] = "maintenance_logs validation failed: " . $e->getMessage();
            echo "   ✗ Validation failed\n";
        }
    }
    
    private function validateTicketsTable() {
        echo "3. Validating tickets table structure...\n";
        
        try {
            // Check if device_id allows NULL
            $stmt = $this->pdo->query("DESCRIBE tickets");
            $columns = $stmt->fetchAll();
            
            $deviceIdColumn = null;
            foreach ($columns as $column) {
                if ($column['Field'] === 'device_id') {
                    $deviceIdColumn = $column;
                    break;
                }
            }
            
            if ($deviceIdColumn && $deviceIdColumn['Null'] === 'YES') {
                $this->success[] = "tickets.device_id correctly allows NULL values";
                echo "   ✓ device_id field allows NULL (supports tickets without devices)\n";
            } else {
                $this->errors[] = "tickets.device_id should allow NULL values";
                echo "   ✗ device_id field doesn't allow NULL\n";
            }
        } catch (Exception $e) {
            $this->errors[] = "tickets validation failed: " . $e->getMessage();
            echo "   ✗ Validation failed\n";
        }
    }
    
    private function validateUsersTable() {
        echo "4. Validating users table structure...\n";
        
        try {
            // Check if permissions field exists and is JSON type
            $stmt = $this->pdo->query("DESCRIBE users");
            $columns = $stmt->fetchAll();
            
            $permissionsColumn = null;
            foreach ($columns as $column) {
                if ($column['Field'] === 'permissions') {
                    $permissionsColumn = $column;
                    break;
                }
            }
            
            if ($permissionsColumn && strpos($permissionsColumn['Type'], 'json') !== false) {
                $this->success[] = "users.permissions field is correctly configured as JSON";
                echo "   ✓ permissions field is JSON type (supports granular permissions)\n";
            } else {
                $this->errors[] = "users.permissions should be JSON type";
                echo "   ✗ permissions field is not JSON type\n";
            }
        } catch (Exception $e) {
            $this->errors[] = "users validation failed: " . $e->getMessage();
            echo "   ✗ Validation failed\n";
        }
    }
    
    private function validateHospitalsTable() {
        echo "5. Validating hospitals table structure...\n";
        
        try {
            // Check if all expected fields exist
            $stmt = $this->pdo->query("DESCRIBE hospitals");
            $columns = $stmt->fetchAll(PDO::FETCH_COLUMN);
            
            $expectedFields = ['name', 'address', 'city', 'country', 'phone', 'email', 'website', 'notes'];
            $missingFields = array_diff($expectedFields, $columns);
            
            if (empty($missingFields)) {
                $this->success[] = "hospitals table has all expected fields";
                echo "   ✓ All expected fields present\n";
            } else {
                $this->errors[] = "hospitals table missing fields: " . implode(', ', $missingFields);
                echo "   ✗ Missing fields: " . implode(', ', $missingFields) . "\n";
            }
        } catch (Exception $e) {
            $this->errors[] = "hospitals validation failed: " . $e->getMessage();
            echo "   ✗ Validation failed\n";
        }
    }
    
    private function updateVersionInfo() {
        echo "6. Updating version information...\n";
        
        try {
            // Update or insert version setting
            $stmt = $this->pdo->prepare("
                INSERT INTO settings (setting_key, setting_value, description) 
                VALUES ('system_version', '3.1', 'Current system version with critical fixes')
                ON DUPLICATE KEY UPDATE 
                setting_value = '3.1', 
                description = 'Current system version with critical fixes',
                updated_at = NOW()
            ");
            $stmt->execute();
            
            $this->success[] = "Version updated to 3.1";
            echo "   ✓ System version updated to 3.1\n";
        } catch (Exception $e) {
            $this->errors[] = "Version update failed: " . $e->getMessage();
            echo "   ✗ Version update failed\n";
        }
    }
    
    private function printResults() {
        echo "\n=== MIGRATION RESULTS ===\n";
        
        if (!empty($this->success)) {
            echo "\n✅ SUCCESSFUL OPERATIONS:\n";
            foreach ($this->success as $success) {
                echo "   • $success\n";
            }
        }
        
        if (!empty($this->errors)) {
            echo "\n❌ ISSUES FOUND:\n";
            foreach ($this->errors as $error) {
                echo "   • $error\n";
            }
            
            echo "\n⚠️  IMPORTANT NOTES:\n";
            echo "   • If you see 'missing fields' errors, your installation may already have the correct schema\n";
            echo "   • Fresh installations (v3.1) already include all fixes\n";
            echo "   • Only older installations need manual migration\n";
        }
        
        echo "\n=== NEXT STEPS ===\n";
        echo "1. Run validation tests: php tests/fixes_validation_test.php\n";
        echo "2. Test your application functionality\n";
        echo "3. Monitor error logs for any issues\n";
        echo "4. Update your model files if you haven't already\n";
        
        if (empty($this->errors)) {
            echo "\n🎉 Migration completed successfully!\n";
        } else {
            echo "\n⚠️  Migration completed with issues. Please review the errors above.\n";
        }
    }
}

// Run the migration
$migration = new MigrationV31();
$migration->run();
